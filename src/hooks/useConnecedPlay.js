import useActiveGroupStore from '@/store/useActiveGroupStore';
import { getConnectedPlay } from '@/utils/apiCalls';
import { useQueryClient } from '@tanstack/react-query';
import AgoraRTC from 'agora-rtc-sdk-ng';
export const rtc = {
  client: AgoraRTC.createClient({ mode: 'rtc', codec: 'h264' }),
  localAudioTrack: null,
  remoteAudioTrack: null,
};

const useConnectedPlay = () => {
  const queryClient = useQueryClient();
  const { setConnectedPlay } = useActiveGroupStore();

  const handleConnectedPlay = async (newParams) => {
    const result = await queryClient.fetchQuery({
      queryKey: ['GET_CONNECTED_PLAY', newParams],
      queryFn: () => getConnectedPlay({ enabled: true, ...newParams }),
      select: (data) => data?.data?.response,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
    });
    setConnectedPlay(result?.data?.response);
    return result;
  };

  return {
    handleConnectedPlay,
  };
};

export default useConnectedPlay;
