import { useUserActionMutation } from '@/reactQuery/generalQueries';
import { useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

const useUserActions = ({ refetch }) => {

  const queryClient = useQueryClient();

  const {
    mutate: performUserAction,
    isLoading,
    isError,
    isSuccess,
    data,
    error,
  } = useUserActionMutation({
    onSuccess: (response,variables) => {
      queryClient.invalidateQueries(['GET_PublicChatsQuery','GET_PRIVATE_CHAT_QUERY']);
      queryClient.invalidateQueries(['GET_PRIVATE_CHAT_QUERY']);
      if (Object.keys(variables).includes('ignoreUser')) {
        toast.success(
          `User ${variables.ignoreUser ? 'ignored' : 'Unignored'} Successfully!`,
        );
      }
      refetch();
    },
    onError: (err) => {
      console.error('Error performing user action:', err);
    },
  });

  const ignoreUser = (relationUserId) => {
    performUserAction({ relationUserId, ignoreUser: true });
  };

  const likeUser = (relationUserId) => {
    performUserAction({ relationUserId, likeUser: true });
  };

  const unignoreUser = (relationUserId) => {
    performUserAction({ relationUserId, ignoreUser: false });
  };

  const unlikeUser = (relationUserId) => {
    performUserAction({ relationUserId, likeUser: false });
  };

  return {
    ignoreUser,
    likeUser,
    unignoreUser,
    unlikeUser,
    isLoading,
    isError,
    isSuccess,
    data,
    error,
  };
};

export default useUserActions;
