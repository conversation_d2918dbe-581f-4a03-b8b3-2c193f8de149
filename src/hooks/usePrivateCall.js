import {
  useDeclinedCall,
  useGenerateAgoraToken,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import useCallModalStore from '@/store/useCallModalStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { getAccessToken } from '@/utils/helper';
import { chatRoomSocket, voiceCallConnected } from '@/utils/socket';
import useAudioPlayer from '@/utils/useAudioPlayer';
import AgoraRTC from 'agora-rtc-sdk-ng';
import { useEffect, useRef, useState } from 'react';
export const rtc = {
  client: AgoraRTC.createClient({ mode: 'rtc', codec: 'h264' }),
  localAudioTrack: null,
  remoteAudioTrack: null,
};
const usePrivateCall = () => {
  AgoraRTC.setLogLevel(4);
  const {
    setUserId,
    isCallActive,
    setIsCallActive,
    userId,
    setCallId,
    channelName,
    setChannelName,
  } = usePrivateChatStore((state) => state);
  const accessToken = getAccessToken();
  const [callStatus, setCallStatus] = useState('Incoming Call');
  const [isMuted, setIsMuted] = useState(false);

  useEffect(() => {
    chatRoomSocket.auth = { token: accessToken };
    chatRoomSocket.connect();
    voiceCallConnected.auth = { token: accessToken };
    voiceCallConnected.connect();
    // chatRoomSocket.on('DECLINE_PERSONAL_VOICE_CHAT_CHANNEL', disconnectCall);
    // chatRoomSocket.on('NOT_ATTENDED_VOICE_CALL', disconnectCall);
  }, []);
  // const [channelName, setChannelName] = useState('');
  const { clearModals, openModal } = useModalStore((state) => state);
  const { setVoiceCall, voiceCall } = useVoiceCallStore((state) => state);
  const { audio } = useAudioPlayer();
  const { userDetails } = useAuthStore((state) => state);
  const {
    closeModal: closeCallModal,
    setIsMinimized,
    isMinimized,
  } = useCallModalStore((state) => state);
  const retryCounterRef = useRef(0);

  const tokenGenRetryLimit = 3; // Set retry limit
  // const declineCallOnPageRefreshOrClose = () => {
  //   const payload = {
  //     callLogId: voiceCall.callLogId,
  //     isOneToOneCall: 'true',
  //   };
  //   if (voiceCall?.channelName) {
  //     payload.channelName = voiceCall?.channelName;
  //   } else {
  //     payload.channelName = channelName;
  //     payload.otherCallerId = voiceCall.userId;
  //   }

  //   fetch(`${process.env.NEXT_PUBLIC_API_URL}/agora/decline-call`, {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       // Authorization: `Bearer ${accessToken}`,
  //       accesstoken: accessToken,
  //     },
  //     body: JSON.stringify(payload),
  //     keepalive: true,
  //   });
  // };

  // useEffect(() => {
  //   const handleUnload = () => {
  //     if (isCallActive) {
  //       declineCallOnPageRefreshOrClose();
  //     }
  //   };
  //   window.addEventListener('beforeunload', handleUnload);
  //   return () => {
  //     window.removeEventListener('beforeunload', handleUnload);
  //   };
  // }, [isCallActive]);
  const token_genration = useGenerateAgoraToken({
    onSuccess: async (response) => {
      try {
        console.log(response, ':::::: response');
        setVoiceCall({ ...voiceCall, callLogId: response?.data?.callLogId });

        // First, check for microphone permission before proceeding
        const micPermissionGranted = await checkMicrophonePermission();
        if (!micPermissionGranted) {
          alert('Please allow microphone access to join the call.');
          return; // Exit early if permission is not granted
        }

        // Proceed with Agora setup after permission is confirmed
        await rtc.client.join(
          process.env.NEXT_PUBLIC_AGORA_APP_ID,
          channelName,
          response.data.token,
          userDetails.uniqueId,
        );

        // Create the microphone audio track
        rtc.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
        await rtc.client.publish(rtc.localAudioTrack);

        chatRoomSocket.emit('USER_VOICE_CALL_CONNECTED', {
          isConnected: true,
          callLogId: response?.data?.callLogId,
          channelName,
          userId: userDetails.uniqueId,
          token: response.data.token,
        });

        setIsCallActive(true);
        setCallId(userId);
        retryCounterRef.current = 0;
      } catch (err) {
        console.error('Join or publish error:', err);
        token_genration.mutate({
          channelName,
          role: 'publisher',
          callReceiverId: userId,
        });
      }
    },

    onError: (error) => {
      console.error('Error generating token:', error, retryCounterRef.current);

      if (retryCounterRef.current < tokenGenRetryLimit) {
        retryCounterRef.current += 1;
        setRetryCount(retryCounterRef.current); // Optionally update state
        console.log(`Retry attempt: ${retryCounterRef.current}`);

        // Call the API again if retry count hasn't exceeded limit
        token_genration.mutate({
          channelName,
          role: 'publisher',
          callReceiverId: userId,
        });
      } else {
        console.log('Max retry limit reached. No further retries.');
      }
    },
  });

  // Function to check microphone permission
  const checkMicrophonePermission = async () => {
    try {
      // First, use the Permissions API if supported by the browser
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({
          name: 'microphone',
        });
        if (permission.state === 'granted') {
          return true; // Microphone permission granted
        } else if (permission.state === 'denied') {
          return false; // Microphone permission denied
        }
      }

      // Fallback: Try to get media if Permissions API is not available
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach((track) => track.stop()); // Stop after checking
      return true; // Microphone permission granted
    } catch (err) {
      console.error('Microphone permission error:', err);
      return false; // Permission denied or error occurred
    }
  };

  const initiateCall = async (userID) => {
    setUserId(userID);

    let createChannel = [userDetails?.userId, userID]
      .sort((a, b) => a - b)
      .join('-');
    setChannelName(createChannel);
    try {
      token_genration.mutate({
        channelName: createChannel,
        role: 'publisher',
        callReceiverId: userID + '',
      });
    } catch (error) {
      console.error('Error initiating call:', error);
    }
  };
  const callDisconnect = useDeclinedCall({
    onSuccess: async () => {
      clearModals();
    },
    onError: (error) => {
      console.error('Error declining call:', error);
    },
  });
  const disconnectCall = async (data) => {
    try {
      audio.pause();
      audio.currentTime = 0;
      console.log(data, ':::::::::::voiceCall', voiceCall);
      const payload = {
        channelName,
        callLogId: voiceCall?.callLogId,
        isOneToOneCall: 'true',
        // otherCallerId: privateChatUserDetails?.userId,
      };
      if (!isCallActive) {
        payload.notAttended = 'true';
      }
      if (voiceCall?.callLogId) await callDisconnect.mutate(payload);

      rtc.localAudioTrack?.close();
      await rtc.client.leave();
      setVoiceCall(null);
      setIsCallActive(false);
      setCallId(null);
      setChannelName('');
    } catch (error) {
      console.error('Error disconnecting call:', error);
    } finally {
      audio.pause();
      audio.currentTime = 0;
    }
  };
  const token_genration_accept_call = useGenerateAgoraToken({
    onSuccess: async (response) => {
      await rtc.client.join(
        process.env.NEXT_PUBLIC_AGORA_APP_ID,
        voiceCall.channelName,
        response.data.token,
        userDetails.uniqueId,
      );
      console.log('🚀 ~ onSuccess: ~ voiceCall:', voiceCall);

      rtc.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
      await rtc.client.publish(rtc.localAudioTrack);
      setCallStatus('Call Connected');
      setIsCallActive(true);
      setCallId(voiceCall?.userId);
      retryCounterRef.current = 0;
    },
    onError: (error) => {
      console.error('Error generating token:', error);
      if (retryCounterRef.current < tokenGenRetryLimit) {
        retryCounterRef.current += 1;
        setRetryCount(retryCounterRef.current); // Optionally update state
        console.log(`Retry attempt: ${retryCounterRef.current}`);

        // Call the API again if retry count hasn't exceeded limit
        token_genration_accept_call.mutate({
          channelName: voiceCall.channelName,
          role: 'subscriber',
          callLogId: voiceCall.callLogId,
          answer: 'answer',
        });
      } else {
        console.log('Max retry limit reached. No further retries.');
      }
    },
  });
  const declineCall = useDeclinedCall({
    onSuccess: async () => {},
    onError: (error) => {
      console.error('Error declining call:', error);
    },
  });

  const handleAcceptCall = async () => {
    try {
      token_genration_accept_call.mutate({
        channelName: voiceCall.channelName,
        role: 'subscriber',
        callLogId: voiceCall.callLogId,
        answer: 'answer',
      });
    } catch (error) {
      console.error('Error accepting call:', error);
    } finally {
      audio.pause();
      audio.currentTime = 0;
    }
  };

  const handleDeclineCall = async () => {
    try {
      let payload = {
        channelName: voiceCall.channelName,
        callLogId: voiceCall.callLogId,
        otherCallerId: voiceCall.userId,
        isOneToOneCall: 'true',
      };
      if (!isCallActive) {
        payload.notAttended = 'true';
      }
      declineCall.mutate(payload);
      audio.pause();
      audio.currentTime = 0;
      rtc.localAudioTrack?.close();
      await rtc.client.leave();
      console.log('iiiiiiiiiii555555555');

      setVoiceCall(null);
      setIsCallActive(false);
      setCallId(null);
      setChannelName('');
    } catch (error) {
      console.error('Error declining call:', error);
    } finally {
      closeCallModal();
    }
  };
  const handleDisconnectCall = async () => {
    if (voiceCall?.channelName) {
      handleDeclineCall();
    } else {
      disconnectCall();
    }
  };
  return {
    initiateCall,
    disconnectCall,
    isCallActive,
    handleAcceptCall,
    handleDeclineCall,
    callStatus,
    setCallStatus,
    isMuted,
    setIsMuted,
    handleDisconnectCall,
  };
};

export default usePrivateCall;
