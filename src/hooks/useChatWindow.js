import { useState, useEffect, useRef, useCallback } from 'react';
import toast from 'react-hot-toast';
import { io } from 'socket.io-client';
import { GiphyFetch } from '@giphy/js-fetch-api';
import {
  useGetGroupChatsQuery,
  useGetPrivateChatsQuery,
  useGetPublicChatsQuery,
  useGetUserTagsQuery,
  useSendPublicMsgMutation,
  useSendTagMsgMutation,
} from '@/reactQuery/chatWindowQuery';
import usePublicChatsStore from '@/store/usePublicChatStore';
import {
  getGroupChatSocket,
  getPrivateChatSocket,
  groupChatsSocket,
  liveChatsSocket,
  playerSocket,
} from '@/utils/socket';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useAuthStore from '@/store/useAuthStore';
import { getAccessToken } from '@/utils/helper';
import useGroupChatStore from '@/store/useGroupChatStore';
import useIgnoredUsers from './useIgnoredUsers';
import useChatSection from './useChatSection';

const gf = new GiphyFetch('Qn2QLQaEOJz3cBebrCe8bDFLWyDnTjCo');

const useChatWindow = () => {
  const [message, setMessage] = useState('');
  const [error, setError] = useState('')
  const [gifMessage, setGifMessage] = useState('');
  const [searchTag, setSearchTag] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showGifPicker, setShowGifPicker] = useState(false);
  const { section, setSection } = useChatSection();
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const [taggedUser, setTaggedUser] = useState(null);
  const [newMessagesCount, setNewMessagesCount] = useState(0);
  const [newGroupMessagesCount, setNewGroupMessagesCount] = useState(0);
  const [grabbedChat, setGrabbedChat] = useState(null);
  const { userDetails, isAuthenticated } = useAuthStore();
  const chatContainerRef = useRef(null);
  const groupChatContainerRef = useRef(null);
  const inputRef = useRef(null);
  const suggestionsRef = useRef([]);
  const initialLoad = useRef(true);
  const groupChatInitialLoad = useRef(true);
  const prevScrollHeight = useRef(0);
  const prevScrollHeightInGroup = useRef(0);
  const prevTop = useRef(false);
  const prevTopInGroup = useRef(false);
  const { ignoredUsers } = useIgnoredUsers();
  const accessToken = getAccessToken();
  // const fetchGifs = (offset) => gf.trending({ offset, limit: 10 });
  // const searchKey = (query, offset) => gf.search(query, { offset, limit: 10 });
  const [searchQuery, setSearchQuery] = useState('');
  const fetchGifs = async (offset) => {
    if (searchQuery.trim()) {
      console.log(searchQuery, ':::::::::fetchGifs search work');
      return await gf.search(searchQuery, { offset, limit: 10 });
    }
    return await gf.trending({ offset, limit: 10 });
  };
  // fetchGifs().then(data => console.log(data,":::::::::fetchGifs search done"))
  const {
    chats: publicChats,
    setChats,
    appendChat,
    updateRainChat,
    updateGrabbedRainChat,
  } = usePublicChatsStore((state) => state);

  const {
    groupChat,
    groupPageChat,
    setGroupChat,
    setGroupPageChat,
    appendGroupChat,
    appendGroupPageChat,
    groupId,
    groupName,
    setIsGroupChatOpen,
    isGroupChatOpen,
    setLivePlayersCountInGroup,
    livePlayersCountInGroup,
    isGroupPageOpen,
    setIsGroupPageOpen,
  } = useGroupChatStore((state) => state);

  const {
    privateChat,
    setPrivateChat,
    setRecipientUser,
    appendPrivateChat,
    isPrivateChatOpen,
    userId,
    recipientUser,
    setIsPrivateChatOpen,
  } = usePrivateChatStore((state) => state);

  const {
    data,
    isLoading: isPublicChatsLoading,
    fetchNextPage,
    hasNextPage,
    refetch: refetchPublicChats,
  } = useGetPublicChatsQuery({ enabled: true });

  const {
    data: groupData,
    isLoading: isGroupChatsLoading,
    fetchNextPage: fetchNextGroupPage,
    hasNextPage: hasNextGroupChat,
    refetch: refetchGroupChats,
  } = useGetGroupChatsQuery({
    groupId,
    enabled: !!(isAuthenticated && groupId),
  });

  const { data: privateChatData } = useGetPrivateChatsQuery({
    enabled: !!(isPrivateChatOpen && userId),
    receiverId: userId,
  });

  const { setLivePlayersCount, livePlayersCount } = usePublicChatsStore(
    (state) => state,
  );

  const { data: tagSuggestion } = useGetUserTagsQuery({
    enabled: !!searchTag,
    params: {
      search: searchTag,
    },
  });

  // useEffect(() => {
  //   if(isAuthenticated ){
  //   refetchPublicChats({enabled:isAuthenticated});
  //   }
  //   else{
  //     setSection("PublicChat")
  //   }

  // }, [section, userDetails]);

  const sendMsgMutation = useSendPublicMsgMutation({
    onSuccess: (response) => {
      console.log('Message sent successfully:', response);
    },
    onError: (error) => {
      console.log('Error sending message:', error);
    },
  });

  const sendPrivateMsgMutation = useSendPublicMsgMutation({
    onSuccess: (response) => {
      console.log('Message sent successfully:', response);
    },
    onError: (error) => {
      console.log('Error sending message:', error);
    },
  });

  const sendGroupMsgMutation = useSendPublicMsgMutation({
    onSuccess: (response) => {
      console.log('Message sent successfully:', response);
    },
    onError: (error) => {
      console.log('Error sending message:', error);
    },
  });

  const tagMutation = useSendTagMsgMutation({
    onSuccess: (response) => {
      console.log('Message sent successfully:', response);
    },
    onError: (error) => {
      console.log('Error sending message:', error);
    },
  });
  const scrollToTop = (smooth = false) => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: 0,
        behavior: smooth ? 'smooth' : 'auto',
      });
    }
  };
  const scrollToBottom = (smooth = false) => {
    if (chatContainerRef.current && section == "PublicChat") {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto',
      });
    }
    else {
      scrollToTop()
    }
  };

  const scrollToBottomGroup = (smooth = false) => {
    if (groupChatContainerRef.current) {
      groupChatContainerRef.current.scrollTo({
        top: groupChatContainerRef.current.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto',
      });
    }
  };

  const scrollToTopGroup = (smooth = false) => {
    if (groupChatContainerRef.current) {
      groupChatContainerRef.current.scrollTo({
        top: 0,
        behavior: smooth ? 'smooth' : 'auto',
      });
    }
  };
  useEffect(() => {
    if (data?.pages) {
      setChats(data.pages);
      setLivePlayersCount(data.livePlayersCount);
      if (initialLoad.current) {
        scrollToBottom();
        initialLoad.current = false;
      }
    }
  }, [data, setChats]);

  useEffect(() => {
    // groupChatInitialLoad
    if (groupId && groupData?.pages) {
      setGroupChat(groupData.pages);
      setGroupPageChat(groupData.pages)
      setLivePlayersCountInGroup(groupData.livePlayersCount);
      if (groupChatInitialLoad.current) {
        scrollToBottomGroup();
        groupChatInitialLoad.current = false;
      }
    } else if (groupData?.pages?.length == 0) {
      setGroupChat(groupData.pages || []);
    }
  }, [groupId, groupData, setGroupChat, setGroupPageChat]);

  useEffect(() => {
    if (privateChatData?.data) {
      setPrivateChat(privateChatData?.data?.chatDetails?.rows);
      setRecipientUser(privateChatData?.data?.chatDetails?.recipientUser);
    }
  }, [privateChatData, setPrivateChat]);

  const isInitialized = useRef(false);

  useEffect(() => {
    // Prevent multiple event bindings
    if (isInitialized.current) return;
    isInitialized.current = true;

    liveChatsSocket.connect();
    playerSocket.connect();

    const handleNewMessage = (newMessage) => {
      console.log(
        'handleNewMessage',
        newMessage,
        newMessage?.data.message == 'RAIN_COMPLETED',
      );
      console.log('logged in user ', userDetails);

      if (
        newMessage?.data?.message === 'tip' &&
        newMessage?.data?.moreDetails?.receiverId == userDetails?.userId
      ) {
        toast.success(
          `Just received a tip of ${newMessage?.data?.moreDetails?.amount} AC`,
        );
      }

      if (newMessage?.data.message == 'RAIN_COMPLETED') {
        updateRainChat(newMessage?.data);
      } else {
        console.log('its in public chat');
        appendChat(newMessage?.data);
      }
    };

    const handleNotification = (notification) => {
      toast.success(`${notification?.data?.taggedBy} tagged you!`);
    };

    // Check if event listeners already exist to avoid duplicates

    // liveChatsSocket.off('/live-chats/viewLiveChats', handleNewMessage);

    if (!liveChatsSocket.hasListeners('/live-chats/viewLiveChats')) {
      liveChatsSocket.on('/live-chats/viewLiveChats', handleNewMessage);
    }

    if (!playerSocket.hasListeners('TAG_PLAYER')) {
      playerSocket.on('TAG_PLAYER', handleNotification);
    }

    return () => {
      liveChatsSocket.off('/live-chats/viewLiveChats', handleNewMessage);
      playerSocket.off('TAG_PLAYER', handleNotification);
      liveChatsSocket.disconnect();
      playerSocket.disconnect();
      isInitialized.current = false; // Reset flag on cleanup
    };
  }, []);

  useEffect(() => {
    const handleNewMessage = (newMessage) => {
      appendPrivateChat(newMessage?.data);
    };

    if (isPrivateChatOpen) {
      const socket = getPrivateChatSocket(userId, accessToken);
      socket.off('PRIVATE_CHAT');
      socket.on('PRIVATE_CHAT', handleNewMessage);

      return () => {
        socket.off('PRIVATE_CHAT', handleNewMessage);
      };
    }
  }, [isPrivateChatOpen, userId]);

  useEffect(() => {
    const handleNewMessage = (newMessage) => {
      appendGroupChat(newMessage?.data);
      appendGroupPageChat(newMessage?.data)
    };
    groupChatsSocket.auth = { token: accessToken };
    // groupChatsSocket.query = {groupName:groupName}
    // groupChatsSocket.connect();
    // groupChatsSocket.on('GROUP_CHAT',handleNewMessage)

    if (isGroupChatOpen) {
      const socket = getGroupChatSocket(groupName, accessToken);
      socket.off('GROUP_CHAT');
      socket.on('GROUP_CHAT', handleNewMessage);

      return () => {
        socket.off('GROUP_CHAT', handleNewMessage);
      };
    }
  }, [
    isGroupChatOpen,
    groupName,
    isGroupPageOpen,
    accessToken,
    appendGroupChat,
    appendGroupPageChat,
    isGroupPageOpen
  ]);

  // useEffect(() => {
  //   const handleNewMessage = (newMessage) => {
  //     console.log(newMessage, "::::::::newMessage here",groupName)
  //     appendGroupChat(newMessage?.data);
  //   };
  //   groupChatsSocket.auth = { token: accessToken };
  //   // groupChatsSocket.query = {groupName:groupName}
  //   // groupChatsSocket.connect();
  //   // groupChatsSocket.on('GROUP_CHAT',handleNewMessage)

  //   let socket;
  //   if (isGroupChatOpen) {
  //     socket = io(`${process.env.NEXT_PUBLIC_SOCKET_URL}/chat-room`, {
  //       query: { groupName },
  //       transports: ['websocket'],
  //       withCredentials: true,
  //       auth: {
  //         token: accessToken,  // Replace `userToken` with the actual token variable
  //       },
  //     });
  //     socket.auth = { token: accessToken };
  //     socket.on('GROUP_CHAT', handleNewMessage);
  //   }
  //   return () => {
  //     if (socket) {
  //       socket.off('GROUP_CHAT', handleNewMessage);
  //       socket.disconnect();
  //     }
  //   };
  // }, [isGroupChatOpen,groupName]);

  const isScrolledToBottom = () => {
    if (!chatContainerRef.current) return false;
    return (
      chatContainerRef.current.scrollHeight -
      chatContainerRef.current.scrollTop -
      chatContainerRef.current.clientHeight <
      200
    );
  };

  const isScrolledToBottomInGroup = () => {
    if (!groupChatContainerRef.current) return false;
    return (
      groupChatContainerRef.current.scrollHeight -
      groupChatContainerRef.current.scrollTop -
      groupChatContainerRef.current.clientHeight <
      200
    );
  };

  useEffect(() => {
    if (prevTop.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight - prevScrollHeight.current;
      prevTop.current = false;
    } else if (publicChats && section === 'PublicChat') {
      const wasScrolledToBottom = isScrolledToBottom();
      if (wasScrolledToBottom) {
        scrollToBottom(true);
        setNewMessagesCount(0);
      } else {
        setNewMessagesCount((prev) => prev + 1);
        console.log('New public messages:', newMessagesCount);
      }
    }
  }, [publicChats]);

  useEffect(() => {
    if (prevTopInGroup.current && groupChatContainerRef.current) {
      groupChatContainerRef.current.scrollTop =
        groupChatContainerRef.current.scrollHeight - prevScrollHeightInGroup.current;
      prevTopInGroup.current = false;
    } else if (groupChat && section === 'GroupChat') {
      const wasScrolledToBottom = isScrolledToBottomInGroup();
      if (wasScrolledToBottom) {
        scrollToBottomGroup(true);
        setNewGroupMessagesCount(0);
      } else {
        setNewGroupMessagesCount((prev) => prev + 1);
        console.log('New group messages:', newGroupMessagesCount);
      }
    }
  }, [groupChat, groupPageChat]);

  useEffect(() => {
    if (chatContainerRef.current) {
      scrollToBottom();
    }
    if (section == 'GroupChat') {
      scrollToTopGroup();
    }
    setError('')
  }, [section]);

  const handleScroll = useCallback(() => {
    if (chatContainerRef.current.scrollTop === 0 && hasNextPage && section == "PublicChat") {
      prevScrollHeight.current = chatContainerRef.current.scrollHeight;
      fetchNextPage();
      prevTop.current = true;
    } else if (isScrolledToBottom()) {
      setNewMessagesCount(0);
    }
  }, [fetchNextPage, hasNextPage, section]);

  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (chatContainer) {
      chatContainer.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (chatContainer) {
        chatContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, [handleScroll, hasNextPage]);

  const handleScrollForGroup = useCallback(() => {
    if (!groupChatContainerRef.current) return;

    if (groupChatContainerRef.current.scrollTop === 0 && hasNextPage) {
      prevScrollHeightInGroup.current = groupChatContainerRef.current.scrollHeight;
      fetchNextPage();
      prevTopInGroup.current = true;
    } else if (isScrolledToBottomInGroup()) {
      setNewGroupMessagesCount(0);
    }
  }, [fetchNextPage, hasNextPage]);

  useEffect(() => {
    const groupChatContainer = groupChatContainerRef.current;
    if (groupChatContainer) {
      groupChatContainer.addEventListener('scroll', handleScrollForGroup);
    }
    return () => {
      if (groupChatContainer) {
        groupChatContainer.removeEventListener('scroll', handleScrollForGroup);
      }
    };
  }, [handleScrollForGroup, hasNextPage]);

  useEffect(() => {
    if (isPrivateChatOpen) {
      setSection('PrivateChat');
    }
  }, [isPrivateChatOpen]);

  useEffect(() => {
    if (showSuggestions) {
      setSelectedSuggestion(0);
    }
  }, [showSuggestions]);

  const handleInputChange = (e) => {
    setError('')
    const { value } = e.target;
    if (!gifMessage) {
      setMessage(value);
    }

    const findTaggedText = value.split(' ').slice(-1)[0];
    const mentionIndex = findTaggedText.lastIndexOf('@');

    if (mentionIndex !== -1) {
      const mentionText = findTaggedText.slice(mentionIndex);
      if (mentionText.length > 1 && !taggedUser) {
        setSearchTag(mentionText.slice(1).toLowerCase());
        setShowSuggestions(true);
      } else {
        setSearchTag('');
        setShowSuggestions(false);
      }
    } else {
      setSearchTag('');
      setShowSuggestions(false);
    }
  };

  const sendMessage = (msg) => {
    sendMsgMutation.mutate({ message: msg });
  };

  const sendPrivateMessage = (msg) => {
    sendPrivateMsgMutation.mutate({
      message: msg,
      isPrivate: true,
      receiverId: `${userId}`,
    });
  };
  // sendGroupMsgMutation
  const sendGroupMessage = (msg) => {
    sendGroupMsgMutation.mutate({
      message: msg,
      groupId: `${groupId}`,
    });
  };

  const sendTagMessage = (userUniqueId) => {
    tagMutation.mutate({ userUniqueId });
  };

  const selectSuggestion = (index) => {
    const mentionIndex = message.lastIndexOf('@');
    const newMessage = `${message.slice(0, mentionIndex + 1) + tagSuggestion[index].username} `;
    setMessage(newMessage);
    setTaggedUser(tagSuggestion[index]);
    setSearchTag('');
    setShowSuggestions(false);
    inputRef.current.focus();
  };

  const handleSendMessage = () => {
    if (message.trim() || gifMessage) {
      if (isGroupPageOpen) {
        sendGroupMessage(gifMessage || message);
      } else if (section === 'PublicChat') {
        sendMessage(gifMessage || message);
        if (taggedUser?.uniqueId) sendTagMessage(taggedUser?.uniqueId);
      } else if (section === 'PrivateChat') {
        sendPrivateMessage(gifMessage || message);
      } else if (section === 'GroupChat' || section === 'GroupPage') {
        sendGroupMessage(gifMessage || message);
      }
      setMessage('');
      setGifMessage(null);
      setTaggedUser(null);
      setShowEmojiPicker(false);
      setShowGifPicker(false);
    } else{
      setError('Please enter a message to send')
    }
  };

  const handleKeyDown = (e) => {
    if (showSuggestions) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedSuggestion((prev) => {
          const newIndex = (prev + 1) % tagSuggestion.length;
          return newIndex;
        });
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedSuggestion((prev) => {
          const newIndex =
            (prev - 1 + tagSuggestion.length) % tagSuggestion.length;
          return newIndex;
        });
      } else if (e.key === 'Enter') {
        e.preventDefault();
        selectSuggestion(selectedSuggestion);
      }
    } else if (e.key === 'Enter') {
      e.preventDefault();
      handleSendMessage();
    } 
    else if (e.key === 'Backspace') {
      const input = e.target;
      const hasSelection = input.selectionStart !== input.selectionEnd;
      const cursorAtEnd = input.selectionStart === message.length;
    
      if (hasSelection) {
        return;
      }
    
      const mentionIndex = message.lastIndexOf('@');
      if (cursorAtEnd) {
        e.preventDefault();
        e.stopPropagation();
    
        if (message.endsWith(' ')) {
          setMessage((prev) => prev.slice(0, -1));
          return;
        }
        if (
          mentionIndex !== -1 &&
          message.slice(mentionIndex).indexOf(' ') === -1 &&
          message.slice(mentionIndex).length === message.length - mentionIndex
        ) {
          const messageBeforeTag = message.slice(0, mentionIndex);
          setMessage(messageBeforeTag);
          setSearchTag('');
          setShowSuggestions(false);
          setTaggedUser(null);
        } else {
          setMessage((prevMessage) => prevMessage.slice(0, -1));
        }
      }
    }
  };
  const handleEmojiPickerToggle = () => {
    setShowEmojiPicker(!showEmojiPicker);
    setShowGifPicker(false);
  };

  const handleGifPickerToggle = () => {
    setShowGifPicker(!showGifPicker);
    setShowEmojiPicker(false);
  };

  const handleGifSelect = (gif, event) => {
    event.preventDefault();
    const gifUrl = gif.images.fixed_height.url;
    setGifMessage(gifUrl);
    setShowGifPicker(false);
    setMessage('');
  };

  return {
    message,
    setMessage,
    gifMessage,
    setGifMessage,
    showEmojiPicker,
    setShowEmojiPicker,
    showGifPicker,
    setShowGifPicker,
    section,
    setSection,
    showSuggestions,
    selectedSuggestion,
    groupChat,
    isGroupChatsLoading,
    publicChats: publicChats.filter(
      (d) =>
        !ignoredUsers?.rows
          ?.map((row) => row?.relationUserId)
          ?.includes(Number(d?.userId || d?.id)),
    ),
    isPublicChatsLoading,
    chatContainerRef,
    groupChatContainerRef,
    inputRef,
    suggestionsRef,
    tagSuggestion,
    handleKeyDown,
    selectSuggestion,
    handleSendMessage,
    privateChat,
    recipientUser,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    isPrivateChatOpen,
    livePlayersCount,
    livePlayersCountInGroup,
    newMessagesCount,
    newGroupMessagesCount,
    scrollToBottom,
    fetchNextPage,
    fetchNextGroupPage,
    handleGifSelect,
    fetchGifs,
    setSearchQuery,
    searchQuery,
    setGrabbedChat,
    grabbedChat,
    updateGrabbedRainChat,
    setIsPrivateChatOpen,
    setIsGroupChatOpen,
    setIsGroupPageOpen,
    isGroupPageOpen,
    error
  };
};

export default useChatWindow;
