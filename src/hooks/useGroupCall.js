import GroupCallPopup from '@/components/Models/GroupCallPopup';
import {
  useDeclinedCall,
  useGenerateAgoraToken,
} from '@/reactQuery/chatWindowQuery';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import useAuthStore from '@/store/useAuthStore';
import useCallModalStore from '@/store/useCallModalStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { getAccessToken } from '@/utils/helper';
import { chatRoomSocket, voiceCallConnected } from '@/utils/socket';
import useAudioPlayer from '@/utils/useAudioPlayer';
import { useQueryClient } from '@tanstack/react-query';
import AgoraRTC from 'agora-rtc-sdk-ng';
import { useEffect, useRef, useState } from 'react';
import useConnectedPlay from './useConnecedPlay';
export const rtc = {
  client: AgoraRTC.createClient({ mode: 'rtc', codec: 'h264' }),
  localAudioTrack: null,
  remoteAudioTrack: null,
};

const useGroupCall = () => {
  const queryClient = useQueryClient();
  AgoraRTC.setLogLevel(4);
  // const { setIsPrivateChatOpen, setUserId, isCallActive,
  //     setIsCallActive, userId, searchUserName, setSearchUserName } = usePrivateChatStore(
  //         (state) => state,
  //     );
  const {
    setIsPrivateChatOpen,
    setUserId,
    isCallActive,
    setIsCallActive,
    userId,
    searchUserName,
    setSearchUserName,
    groupCallDetails,
    setGroupCallDetails,
  } = useGroupChatStore((state) => state);
  const accessToken = getAccessToken();
  const {
    closeModal: closeCallModal,
    setIsMinimized,
    isMinimized,
    openModal: openCallModal,
  } = useCallModalStore((state) => state);
  const { setConnectedPlay } = useActiveGroupStore();
  const [isMuted, setIsMuted] = useState(false);
  const retryCounterRef = useRef(0);

  const tokenGenRetryLimit = 3; // Set retry limit
  useEffect(() => {
    chatRoomSocket.auth = { token: accessToken };
    chatRoomSocket.connect();
    voiceCallConnected.auth = { token: accessToken };
    voiceCallConnected.connect();
    chatRoomSocket.on('DECLINE_PERSONAL_VOICE_CHAT_CHANNEL', disconnectCall);
    chatRoomSocket.on('NOT_ATTENDED_VOICE_CALL', disconnectCall);
  }, []);
  const [channelName, setChannelName] = useState('');
  const { clearModals, openModal } = useModalStore((state) => state);
  const { setVoiceCall, voiceCall, updateGroupCallMembers } = useVoiceCallStore(
    (state) => state,
  );
  const { audio, pauseAudio } = useAudioPlayer();
  const { userDetails } = useAuthStore((state) => state);
  const { handleConnectedPlay } = useConnectedPlay();

  //Call decline on page referesh or close
  const declineCallOnPageRefreshOrClose = () => {
    const payload = {
      channelName: voiceCall?.channelName,
      callLogId: voiceCall?.callLogId,
      otherCallerId: voiceCall?.userId,
      isOneToOneCall: 'true',
      groupId: voiceCall?.groupId,
      notAttended: !isCallActive ? 'true' : undefined,
    };
    fetch(`${process.env.NEXT_PUBLIC_API_URL}/agora/decline-call`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Authorization: `Bearer ${accessToken}`,
        accesstoken: accessToken,
      },
      body: JSON.stringify(payload),
      keepalive: true,
    });
  };

  useEffect(() => {
    const handleUnload = () => {
      if (isCallActive) {
        declineCallOnPageRefreshOrClose();
      }
    };
    window.addEventListener('beforeunload', handleUnload);
    return () => {
      window.removeEventListener('beforeunload', handleUnload);
    };
  }, [isCallActive]);

  const token_genration = useGenerateAgoraToken({
    onSuccess: async (response, variables) => {
      console.log(response, ':::::: response', variables);
      if (!response?.data?.oldCallLog) {
        setVoiceCall({
          ...voiceCall,
          callLogId: response?.data?.callLogId,
          channelName: variables.channelName,
          groupId: variables.groupId,
        });
        await rtc.client.join(
          process.env.NEXT_PUBLIC_AGORA_APP_ID,
          channelName,
          response.data.token,
          userDetails.uniqueId,
        );

        rtc.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
        await rtc.client.publish(rtc.localAudioTrack);

        chatRoomSocket.emit('USER_GROUP_VOICE_CALL_CONNECTED', {
          isConnected: true,
          callLogId: response?.data?.callLogId,
          channelName,
          userId: userDetails.uniqueId,
          token: response.data.token,
        });

        setIsCallActive(true);
        retryCounterRef.current = 0;

      } else {
        handleJoinCall({
          channelName: variables.channelName,
          callLogId: response?.data?.callLogId,
          groupId: variables.groupId,
          isConnectedPlay: false,
        });
      }
    },
    onError: (error) => {
      console.error('Error generating token:', error);
      if (retryCounterRef.current < tokenGenRetryLimit) {
        retryCounterRef.current += 1;
        // setRetryCount(retryCounterRef.current); // Optionally update state
        console.log(`Retry attempt: ${retryCounterRef.current}`);

        // Call the API again if retry count hasn't exceeded limit
        token_genration.mutate({
          channelName,
          role: 'publisher',
          callReceiverId: userId,
        });
      } else {
        console.log('Max retry limit reached. No further retries.');
      }
    },
  });

  const initiateCall = async ({ groupId, groupName }) => {
    setUserId(groupId);
    setGroupCallDetails({
      groupName,
      groupId,
    });
    // let createChannel = [userDetails?.userId, userID]
    //     .sort((a, b) => a - b)
    //     .join('-')
    setChannelName(groupName);
    try {
      token_genration.mutate({
        channelName: groupName,
        role: 'publisher',
        groupId: groupId + '',
      });
    } catch (error) {
      console.error('Error initiating call:', error);
    }
  };
  const declineCall = useDeclinedCall({
    onSuccess: async () => {
      clearModals();
    },
    onError: (error) => {
      console.error('Error declining call:', error);
    },
  });
  const disconnectCall = async (data) => {
    try {
      pauseAudio();
      audio.pause();
      audio.currentTime = 0;
      console.log(data, ':::::::::::voiceCall', voiceCall);
      const payload = {
        channelName,
        callLogId: voiceCall?.callLogId,
        isOneToOneCall: 'true',
        // otherCallerId: privateChatUserDetails?.userId,
      };
      if (!isCallActive) {
        payload.notAttended = 'true';
      }
      if (voiceCall?.callLogId) await declineCall.mutate(payload);

      rtc.localAudioTrack?.close();
      await rtc.client.leave();
      // setVoiceCall(null)
      setIsCallActive(false);
    } catch (error) {
      console.error('Error disconnecting call:', error);
    } finally {
      audio.pause();
      audio.currentTime = 0;
      console.log('iiiiiiiiiii22222222222');
      setVoiceCall(null);
    }
  };

  const token_genration_accept_call = useGenerateAgoraToken({
    onSuccess: async (response) => {
      await rtc.client.join(
        process.env.NEXT_PUBLIC_AGORA_APP_ID,
        voiceCall.channelName,
        response.data.token,
        userDetails.uniqueId,
      );

      rtc.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
      await rtc.client.publish(rtc.localAudioTrack);
      // setCallStatus('Call Connected');
      setIsCallActive(true);
      pauseAudio();
        retryCounterRef.current = 0;

    },
    onError: (error) => {
      console.error('Error generating token:', error);
      if (retryCounterRef.current < tokenGenRetryLimit) {
        retryCounterRef.current += 1;
        // setRetryCount(retryCounterRef.current); // Optionally update state
        console.log(`Retry attempt: ${retryCounterRef.current}`);

        // Call the API again if retry count hasn't exceeded limit
        token_genration_accept_call.mutate({
          channelName: voiceCall.channelName,
          role: 'subscriber',
          callLogId: voiceCall.callLogId,
          answer: 'answer',
          groupId: voiceCall?.groupId,
        });
      } else {
        console.log('Max retry limit reached. No further retries.');
      }
    },
  });

  const handleAcceptCall = async () => {
    try {
      token_genration_accept_call.mutate({
        channelName: voiceCall.channelName,
        role: 'subscriber',
        callLogId: voiceCall.callLogId,
        answer: 'answer',
        groupId: voiceCall?.groupId,
      });
    } catch (error) {
      console.error('Error accepting call:', error);
    } finally {
      audio.pause();
      audio.currentTime = 0;
      pauseAudio();
    }
  };

  const handleDeclineCall = async () => {
    try {
      let payload = {
        channelName: voiceCall.channelName,
        callLogId: voiceCall.callLogId,
        otherCallerId: voiceCall.userId,
        isOneToOneCall: 'true',
        groupId: voiceCall?.groupId,
      };
      if (!isCallActive) {
        payload.notAttended = 'true';
      }
      if (voiceCall?.callLogId) declineCall.mutate(payload);

      rtc.localAudioTrack?.close();
      await rtc.client.leave();
      console.log('iiiiiiiiiii33333333333');

      closeCallModal();
      setVoiceCall(null);
      setIsCallActive(false);
      pauseAudio();
      setConnectedPlay({});
    } catch (error) {
      console.error('Error declining call:', error);
    } finally {
      closeCallModal();
      console.log('iiiiiiiiiii44444444444');

      setVoiceCall(null);
    }
  };

  const token_genration_join_call = useGenerateAgoraToken({
    onError: (error) => {
      console.error('Error generating token:', error);
    },
  });

  const handleJoinCall = async (props) => {
    console.log('props', props);

    const attemptJoin = async () => {
      const response = await token_genration_join_call.mutateAsync({
        channelName: props.channelName,
        role: 'subscriber',
        callLogId: props.callLogId,
        answer: 'answer',
        groupId: props?.groupId,
      });

      await rtc.client.join(
        process.env.NEXT_PUBLIC_AGORA_APP_ID,
        props.channelName,
        response.data.token,
        userDetails.uniqueId,
      );

      rtc.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
      await rtc.client.publish(rtc.localAudioTrack);
      setIsCallActive(true);
      setVoiceCall({
        channelName: props.channelName,
        role: 'subscriber',
        callLogId: props.callLogId,
        userId: userDetails?.userId,
        username: userDetails?.username,
        profileImage: userDetails?.profileImage,
        groupId: props?.groupId,
        groupCallMembers:
          props?.groupCallMembers || response?.data?.allCallMembers,
      });
      openCallModal(<GroupCallPopup />);
      setIsMinimized(false);

      if (props?.isConnectedPlay)
        handleConnectedPlay({
          groupId: props?.groupId,
          callLogId: props.callLogId,
        });
    };

    try {
      await attemptJoin();

      // setIsCallActive(true);
      // openCallModal(<GroupCallPopup />);
      // setIsMinimized(false);

      // setVoiceCall({
      //   channelName: props.channelName,
      //   role: 'subscriber',
      //   callLogId: props.callLogId,
      //   userId: userDetails?.userId,
      //   username: userDetails?.username,
      //   profileImage: userDetails?.profileImage,
      //   groupId: props?.groupId,
      //   groupCallMembers: props?.groupCallMembers,
      // });

      // handleConnectedPlay({
      //   groupId: props?.groupId,
      //   callLogId: props.callLogId,
      // });
    } catch (error) {
      console.error('First attempt failed, retrying...', error);
      try {
        await attemptJoin(); // Retry once
        // setIsCallActive(true);
        // openCallModal(<GroupCallPopup />);
        // setIsMinimized(false);

        // setVoiceCall({
        //   channelName: props.channelName,
        //   role: 'subscriber',
        //   callLogId: props.callLogId,
        //   userId: userDetails?.userId,
        //   username: userDetails?.username,
        //   profileImage: userDetails?.profileImage,
        //   groupId: props?.groupId,
        //   groupCallMembers: props?.groupCallMembers,
        // });
      } catch (retryError) {
        console.error('Retry also failed:', retryError);
        // Optionally show error toast or UI feedback
      }
    }
  };

  useEffect(() => {
    async function deleteSession() {
      await rtc.client.leave();
    }
    deleteSession();

    if (!rtc.client) return;

    rtc.client.on('user-published', async (user, mediaType) => {
      await rtc.client.subscribe(user, mediaType);
      if (mediaType === 'audio') {
        rtc.remoteAudioTrack = user.audioTrack;
        rtc.remoteAudioTrack.play();
      }
    });

    rtc.client.on('user-unpublished', () => {
      if (rtc.remoteAudioTrack) {
        rtc.remoteAudioTrack.stop();
        rtc.remoteAudioTrack = null;
      }
    });

    return () => {
      rtc.client.removeAllListeners();
      // handleDeclineCall()
    };
  }, []);

  return {
    initiateCall,
    disconnectCall,
    isCallActive,
    handleAcceptCall,
    handleDeclineCall,
    handleJoinCall,
    isMuted,
    setIsMuted,
  };
};

export default useGroupCall;
