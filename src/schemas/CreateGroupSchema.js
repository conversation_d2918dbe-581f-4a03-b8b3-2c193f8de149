import { z } from 'zod';

export const CreateGroupSchema = z.object({
  groupName: z
    .string()
    .min(3, 'Group name must be at least 3 characters long')
    .max(50, 'Group name must not exceed 50 characters'),

  groupDescription: z
    .string()
    .min(10, 'Group description must be at least 10 characters long')
    .max(500, 'Group description must not exceed 500 characters'),

  // onlyAdminCanCall: z.boolean(),
  onlyAdminCanAddMembers: z.boolean(),
  onlyAdminCanUpdateGroupDetails: z.boolean(),
  // onlySubAdminCanAddMembers: z.boolean(),
  groupType: z.enum(['public', 'private']).default('private'),
});

export const updateGroupSchema = z.object({
  groupName: z
    .string()
    .min(3, 'Group name must be at least 3 characters long')
    .max(50, 'Group name must not exceed 50 characters'),

  groupDescription: z
    .string()
    .min(10, 'Group description must be at least 10 characters long')
    .max(500, 'Group description must not exceed 500 characters'),

  // onlyAdminCanCall: z.boolean(),
  onlyAdminCanAddMembers: z.boolean(),
  onlyAdminCanUpdateGroupDetails: z.boolean(),
  // onlySubAdminCanAddMembers: z.boolean(),
  // groupType: z.enum(['public', 'private']).default('private')
});
