// utils/praxisConfig.js
// Praxis Payment Gateway Configuration and Utilities

/**
 * Praxis configuration - Replace with your actual values
 */
const PRAXIS_CONFIG = {
  // Replace with your actual Praxis credentials
  merchantId: process.env.REACT_APP_PRAXIS_MERCHANT_ID || 'your_merchant_id',
  apiKey: process.env.REACT_APP_PRAXIS_API_KEY || 'your_api_key',
  environment: process.env.REACT_APP_PRAXIS_ENVIRONMENT || 'sandbox', // 'sandbox' or 'production'
  
  // Praxis SDK URLs
  sdkUrls: {
    sandbox: 'https://sandbox-cashier.praxis.com/v1/cashier.js',
    production: 'https://cashier.praxis.com/v1/cashier.js'
  },
  
  // API endpoints
  apiUrls: {
    sandbox: 'https://sandbox-api.praxis.com/v1',
    production: 'https://api.praxis.com/v1'
  },
  
  // Default configuration
  currency: 'USD',
  locale: 'en',
  theme: 'dark' // 'light' or 'dark'
};

/**
 * Validates Praxis configuration
 * @returns {Object} Validation result with isValid flag and errors array
 */
export function validatePraxisConfig() {
  const errors = [];
  
  if (!PRAXIS_CONFIG.merchantId || PRAXIS_CONFIG.merchantId === 'your_merchant_id') {
    errors.push('Merchant ID is required');
  }
  
  if (!PRAXIS_CONFIG.apiKey || PRAXIS_CONFIG.apiKey === 'your_api_key') {
    errors.push('API Key is required');
  }
  
  if (!['sandbox', 'production'].includes(PRAXIS_CONFIG.environment)) {
    errors.push('Environment must be either "sandbox" or "production"');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Loads Praxis SDK dynamically
 * @returns {Promise} Resolves when SDK is loaded
 */
export function loadPraxisSDK() {
  return new Promise((resolve, reject) => {
    // Check if SDK is already loaded
    if (window.PraxisCashier) {
      resolve(window.PraxisCashier);
      return;
    }
    
    // Create script element
    const script = document.createElement('script');
    script.src = PRAXIS_CONFIG.sdkUrls[PRAXIS_CONFIG.environment];
    script.async = true;
    
    script.onload = () => {
      if (window.PraxisCashier) {
        console.log('✅ Praxis SDK loaded successfully');
        resolve(window.PraxisCashier);
      } else {
        reject(new Error('Praxis SDK failed to initialize'));
      }
    };
    
    script.onerror = () => {
      reject(new Error('Failed to load Praxis SDK'));
    };
    
    // Add script to document
    document.head.appendChild(script);
  });
}

/**
 * Creates Praxis payment configuration
 * @param {Object} params - Payment parameters
 * @returns {Object} Praxis configuration object
 */
export function createPraxisPaymentConfig({
  packageData,
  userDetails,
  paymentMethod,
  onSuccess,
  onFailure,
  onPending,
  onCancel
}) {
  const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  return {
    merchantId: PRAXIS_CONFIG.merchantId,
    apiKey: PRAXIS_CONFIG.apiKey,
    environment: PRAXIS_CONFIG.environment,
    
    // Payment details
    amount: packageData.amount,
    currency: PRAXIS_CONFIG.currency,
    orderId: orderId,
    description: `${packageData.name} - ${packageData.features.join(', ')}`,
    
    // Customer details
    customer: {
      id: userDetails.userId,
      email: userDetails.email,
      firstName: userDetails.firstName || userDetails.username,
      lastName: userDetails.lastName || '',
      phone: userDetails.phone || '',
      country: userDetails.country || 'US',
      city: userDetails.city || '',
      address: userDetails.address || '',
      zipCode: userDetails.zipCode || ''
    },
    
    // Payment method
    paymentMethod: paymentMethod,
    
    // UI configuration
    theme: PRAXIS_CONFIG.theme,
    locale: PRAXIS_CONFIG.locale,
    
    // Callback URLs (for server-to-server notifications)
    callbackUrl: `${window.location.origin}/api/payments/praxis/callback`,
    successUrl: `${window.location.origin}/payment/success`,
    failureUrl: `${window.location.origin}/payment/failure`,
    
    // Event handlers
    onSuccess: (response) => {
      console.log('✅ Praxis payment success:', response);
      onSuccess && onSuccess(response);
    },
    
    onFailure: (error) => {
      console.error('❌ Praxis payment failed:', error);
      onFailure && onFailure(error);
    },
    
    onPending: (response) => {
      console.log('⏳ Praxis payment pending:', response);
      onPending && onPending(response);
    },
    
    onCancel: () => {
      console.log('🚫 Praxis payment cancelled');
      onCancel && onCancel();
    }
  };
}

/**
 * Initializes Praxis payment cashier
 * @param {Object} config - Praxis configuration
 * @returns {Promise} Resolves with cashier instance
 */
export async function initializePraxisPayment(config) {
  try {
    // Ensure SDK is loaded
    if (!window.PraxisCashier) {
      throw new Error('Praxis SDK not loaded');
    }
    
    // Create cashier instance
    const cashier = new window.PraxisCashier({
      merchantId: config.merchantId,
      environment: config.environment,
      
      // Payment configuration
      payment: {
        amount: config.amount,
        currency: config.currency,
        orderId: config.orderId,
        description: config.description,
        paymentMethod: config.paymentMethod
      },
      
      // Customer information
      customer: config.customer,
      
      // UI settings
      ui: {
        theme: config.theme,
        locale: config.locale,
        showPaymentMethods: true,
        showCustomerForm: false // Pre-filled from customer object
      },
      
      // Callbacks
      callbacks: {
        onSuccess: config.onSuccess,
        onFailure: config.onFailure,
        onPending: config.onPending,
        onCancel: config.onCancel
      },
      
      // URLs
      urls: {
        callback: config.callbackUrl,
        success: config.successUrl,
        failure: config.failureUrl
      }
    });
    
    return cashier;
    
  } catch (error) {
    console.error('❌ Error initializing Praxis payment:', error);
    throw error;
  }
}

/**
 * Direct API payment (fallback when SDK is not available)
 * @param {Object} paymentData - Payment data
 * @returns {Promise} Payment response
 */
export async function processPraxisDirectPayment(paymentData) {
  try {
    const apiUrl = PRAXIS_CONFIG.apiUrls[PRAXIS_CONFIG.environment];
    
    const response = await fetch(`${apiUrl}/payments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${PRAXIS_CONFIG.apiKey}`,
        'X-Merchant-ID': PRAXIS_CONFIG.merchantId
      },
      body: JSON.stringify({
        amount: paymentData.amount,
        currency: PRAXIS_CONFIG.currency,
        orderId: `direct_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        description: paymentData.description,
        paymentMethod: paymentData.paymentMethod,
        customer: paymentData.customer,
        callbackUrl: paymentData.callbackUrl
      })
    });
    
    if (!response.ok) {
      throw new Error(`Payment API error: ${response.status}`);
    }
    
    const result = await response.json();
    return result;
    
  } catch (error) {
    console.error('❌ Direct payment error:', error);
    throw error;
  }
}

/**
 * Get payment status
 * @param {string} transactionId - Transaction ID
 * @returns {Promise} Payment status
 */
export async function getPraxisPaymentStatus(transactionId) {
  try {
    const apiUrl = PRAXIS_CONFIG.apiUrls[PRAXIS_CONFIG.environment];
    
    const response = await fetch(`${apiUrl}/payments/${transactionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PRAXIS_CONFIG.apiKey}`,
        'X-Merchant-ID': PRAXIS_CONFIG.merchantId
      }
    });
    
    if (!response.ok) {
      throw new Error(`Status API error: ${response.status}`);
    }
    
    const result = await response.json();
    return result;
    
  } catch (error) {
    console.error('❌ Payment status error:', error);
    throw error;
  }
}

/**
 * Supported payment methods by region
 */
export const PRAXIS_PAYMENT_METHODS = {
  global: ['card', 'crypto', 'e_wallet'],
  europe: ['card', 'bank_transfer', 'e_wallet', 'voucher'],
  asia: ['card', 'crypto', 'e_wallet', 'bank_transfer'],
  americas: ['card', 'crypto', 'e_wallet', 'bank_transfer']
};

/**
 * Get available payment methods for user's region
 * @param {string} region - User's region
 * @returns {Array} Available payment methods
 */
export function getAvailablePaymentMethods(region = 'global') {
  return PRAXIS_PAYMENT_METHODS[region] || PRAXIS_PAYMENT_METHODS.global;
}

/**
 * Format amount for display
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @returns {string} Formatted amount
 */
export function formatPraxisAmount(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

/**
 * Validate payment data
 * @param {Object} paymentData - Payment data to validate
 * @returns {Object} Validation result
 */
export function validatePaymentData(paymentData) {
  const errors = [];
  
  if (!paymentData.amount || paymentData.amount <= 0) {
    errors.push('Amount must be greater than 0');
  }
  
  if (!paymentData.userId) {
    errors.push('User ID is required');
  }
  
  if (!paymentData.packageId) {
    errors.push('Package ID is required');
  }
  
  if (!paymentData.paymentMethod) {
    errors.push('Payment method is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

export default {
  validatePraxisConfig,
  loadPraxisSDK,
  createPraxisPaymentConfig,
  initializePraxisPayment,
  processPraxisDirectPayment,
  getPraxisPaymentStatus,
  getAvailablePaymentMethods,
  formatPraxisAmount,
  validatePaymentData,
  PRAXIS_PAYMENT_METHODS
};