// utils/praxisConfig.js
// Praxis Payment Gateway Configuration and Utilities

/**
 * Praxis configuration - Replace with your actual values
 */
const PRAXIS_CONFIG = {
  // Praxis credentials for Six Sides Global / FansBets
  merchantId: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_ID || 'API-FansbetsUS',
  merchantSecret: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_SECRET || '39BTRoOcVpyL24mbi7Idkmu6Sbc3bcmC',
  applicationKey: process.env.NEXT_PUBLIC_PRAXIS_APPLICATION_KEY || 'fansbets.us',
  apiKey: process.env.NEXT_PUBLIC_PRAXIS_API_KEY || 'fansbets.us', // Same as application key
  environment: process.env.NEXT_PUBLIC_PRAXIS_ENVIRONMENT || 'sandbox', // 'sandbox' or 'production'
  
  // Praxis SDK URLs with multiple fallbacks
  sdkUrls: {
    sandbox: [
      'https://sandbox-cashier.praxiscashier.com/assets/js/cashier.min.js',
      'https://sandbox-cashier.praxiscashier.com/js/cashier.js',
      'https://cashier-sandbox.praxiscashier.com/js/cashier.js',
      'https://demo-cashier.praxiscashier.com/js/cashier.js',
      'https://sandbox-cashier.praxis.com/v1/cashier.js'
    ],
    production: [
      'https://cashier.praxiscashier.com/assets/js/cashier.min.js',
      'https://cashier.praxiscashier.com/js/cashier.js',
      'https://cdn.praxiscashier.com/js/cashier.min.js',
      'https://cashier.praxis.com/v1/cashier.js'
    ]
  },
  
  // API endpoints
  apiUrls: {
    sandbox: 'https://sandbox-api.praxis.com/v1',
    production: 'https://api.praxis.com/v1'
  },
  
  // Default configuration
  currency: 'USD',
  locale: 'en',
  theme: 'dark' // 'light' or 'dark'
};

/**
 * Validates Praxis configuration
 * @returns {Object} Validation result with isValid flag and errors array
 */
export function validatePraxisConfig() {
  console.log('🔍 Validating Praxis configuration...');
  console.log('📋 Current config:', {
    merchantId: PRAXIS_CONFIG.merchantId ? `${PRAXIS_CONFIG.merchantId.substring(0, 8)}...` : 'NOT SET',
    merchantSecret: PRAXIS_CONFIG.merchantSecret ? `${PRAXIS_CONFIG.merchantSecret.substring(0, 8)}...` : 'NOT SET',
    applicationKey: PRAXIS_CONFIG.applicationKey ? `${PRAXIS_CONFIG.applicationKey.substring(0, 8)}...` : 'NOT SET',
    apiKey: PRAXIS_CONFIG.apiKey ? `${PRAXIS_CONFIG.apiKey.substring(0, 8)}...` : 'NOT SET',
    environment: PRAXIS_CONFIG.environment,
  });

  const errors = [];

  if (!PRAXIS_CONFIG.merchantId || PRAXIS_CONFIG.merchantId === 'your_merchant_id') {
    errors.push('Merchant ID is required');
  }

  if (!PRAXIS_CONFIG.merchantSecret || PRAXIS_CONFIG.merchantSecret === 'your_merchant_secret') {
    errors.push('Merchant Secret is required');
  }

  if (!PRAXIS_CONFIG.applicationKey || PRAXIS_CONFIG.applicationKey === 'your_application_key') {
    errors.push('Application Key is required');
  }

  if (!PRAXIS_CONFIG.apiKey || PRAXIS_CONFIG.apiKey === 'your_api_key') {
    errors.push('API Key is required');
  }

  if (!['sandbox', 'production'].includes(PRAXIS_CONFIG.environment)) {
    errors.push('Environment must be either "sandbox" or "production"');
  }

  const result = {
    isValid: errors.length === 0,
    errors
  };

  if (result.isValid) {
    console.log('✅ Praxis configuration is valid');
    console.log('🏪 Merchant ID:', PRAXIS_CONFIG.merchantId);
    console.log('� Merchant Secret configured:', PRAXIS_CONFIG.merchantSecret ? 'Yes' : 'No');
    console.log('�🔑 Application Key:', PRAXIS_CONFIG.applicationKey);
    console.log('🗝️ API Key configured:', PRAXIS_CONFIG.apiKey ? 'Yes' : 'No');
    console.log('🌍 Environment:', PRAXIS_CONFIG.environment);
  } else {
    console.error('❌ Praxis configuration errors:', errors);
  }

  return result;
}

/**
 * Test Praxis configuration and credentials
 */
export function testPraxisCredentials() {
  console.log('🧪 Testing Praxis credentials...');

  const validation = validatePraxisConfig();

  if (validation.isValid) {
    console.log('✅ Credentials loaded successfully:');
    console.log('   � Company: Six Sides Global');
    console.log('   📍 Address: 733 Hunkins Waterfront Jewels, Charlestown, Nevis, KN0802');
    console.log('   �🏪 Merchant ID: API-FansbetsUS');
    console.log('   🔐 Merchant Secret: 39BTRoOcVpyL24mbi7Idkmu6Sbc3bcmC');
    console.log('   🔑 Application Key: fansbets.us');
    console.log('   🌍 Environment: sandbox');
    console.log('   🚀 Ready for payment processing!');
    return true;
  } else {
    console.error('❌ Credential validation failed:', validation.errors);
    return false;
  }
}

/**
 * Loads Praxis SDK dynamically with multiple fallback URLs
 * @returns {Promise} Resolves when SDK is loaded
 */
export function loadPraxisSDK() {
  return new Promise(async (resolve, reject) => {
    console.log('🔄 Loading Praxis SDK...');

    // Check if SDK is already loaded
    if (window.PraxisCashier) {
      console.log('✅ Praxis SDK already loaded');
      resolve(window.PraxisCashier);
      return;
    }

    const sdkUrls = PRAXIS_CONFIG.sdkUrls[PRAXIS_CONFIG.environment];
    console.log(`📋 Trying ${sdkUrls.length} SDK URLs for ${PRAXIS_CONFIG.environment} environment`);

    // Try each URL until one works
    for (let i = 0; i < sdkUrls.length; i++) {
      const url = sdkUrls[i];
      console.log(`🔄 Attempting SDK URL ${i + 1}/${sdkUrls.length}: ${url}`);

      try {
        const success = await new Promise((scriptResolve, scriptReject) => {
          // Check if script already exists
          const existingScript = document.querySelector(`script[src="${url}"]`);
          if (existingScript) {
            console.log('⏳ Script already exists, waiting for load...');
            existingScript.addEventListener('load', () => scriptResolve(true));
            existingScript.addEventListener('error', () => scriptReject(new Error('Existing script failed')));
            return;
          }

          // Create new script element
          const script = document.createElement('script');
          script.src = url;
          script.async = true;

          // Set timeout for each attempt
          const timeout = setTimeout(() => {
            console.warn(`⏰ SDK URL ${i + 1} timed out after 10 seconds`);
            scriptReject(new Error('Script load timeout'));
          }, 10000);

          script.onload = () => {
            clearTimeout(timeout);
            console.log(`📦 Script loaded from URL ${i + 1}`);
            if (window.PraxisCashier) {
              console.log('✅ PraxisCashier found on window');
              scriptResolve(true);
            } else {
              console.warn('⚠️ Script loaded but PraxisCashier not found');
              scriptReject(new Error('PraxisCashier not found after script load'));
            }
          };

          script.onerror = (error) => {
            clearTimeout(timeout);
            console.error(`❌ Failed to load script from URL ${i + 1}:`, error);
            scriptReject(new Error(`Script load failed: ${url}`));
          };

          console.log(`📥 Adding script ${i + 1} to document head`);
          document.head.appendChild(script);
        });

        if (success && window.PraxisCashier) {
          console.log(`✅ Praxis SDK loaded successfully from URL ${i + 1}`);
          resolve(window.PraxisCashier);
          return;
        }
      } catch (error) {
        console.warn(`⚠️ SDK URL ${i + 1} failed: ${error.message}`);
        if (i === sdkUrls.length - 1) {
          // Last URL failed
          console.error('❌ All SDK URLs failed to load');
          reject(new Error(`Failed to load Praxis SDK from any URL. Tried ${sdkUrls.length} URLs.`));
        }
      }
    }
  });
}

/**
 * Test SDK URL accessibility (simple check)
 */
export function testSDKUrls() {
  const sdkUrls = PRAXIS_CONFIG.sdkUrls[PRAXIS_CONFIG.environment];
  console.log('🧪 Testing SDK URL accessibility...');
  console.log(`📋 Environment: ${PRAXIS_CONFIG.environment}`);
  console.log(`📋 URLs to test: ${sdkUrls.length}`);

  sdkUrls.forEach((url, index) => {
    console.log(`${index + 1}. ${url}`);
  });

  console.log('💡 Note: Actual loading will be tested when SDK is needed');
  return sdkUrls;
}

/**
 * Creates Praxis payment configuration
 * @param {Object} params - Payment parameters
 * @returns {Object} Praxis configuration object
 */
export function createPraxisPaymentConfig({
  packageData,
  userDetails,
  paymentMethod,
  onSuccess,
  onFailure,
  onPending,
  onCancel
}) {
  const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  return {
    // Merchant credentials
    merchantId: PRAXIS_CONFIG.merchantId,
    merchantSecret: PRAXIS_CONFIG.merchantSecret,
    applicationKey: PRAXIS_CONFIG.applicationKey,
    apiKey: PRAXIS_CONFIG.apiKey,
    environment: PRAXIS_CONFIG.environment,
    
    // Payment details
    amount: packageData.amount,
    currency: PRAXIS_CONFIG.currency,
    orderId: orderId,
    description: `${packageData.name} - ${packageData.features.join(', ')}`,
    
    // Customer details
    customer: {
      id: userDetails.userId,
      email: userDetails.email,
      firstName: userDetails.firstName || userDetails.username,
      lastName: userDetails.lastName || '',
      phone: userDetails.phone || '',
      country: userDetails.country || 'US',
      city: userDetails.city || '',
      address: userDetails.address || '',
      zipCode: userDetails.zipCode || ''
    },
    
    // Payment method
    paymentMethod: paymentMethod,
    
    // UI configuration
    theme: PRAXIS_CONFIG.theme,
    locale: PRAXIS_CONFIG.locale,
    
    // Callback URLs (for server-to-server notifications)
    callbackUrl: `${window.location.origin}/api/payments/praxis/callback`,
    successUrl: `${window.location.origin}/payment/success`,
    failureUrl: `${window.location.origin}/payment/failure`,
    
    // Event handlers
    onSuccess: (response) => {
      console.log('✅ Praxis payment success:', response);
      onSuccess && onSuccess(response);
    },
    
    onFailure: (error) => {
      console.error('❌ Praxis payment failed:', error);
      onFailure && onFailure(error);
    },
    
    onPending: (response) => {
      console.log('⏳ Praxis payment pending:', response);
      onPending && onPending(response);
    },
    
    onCancel: () => {
      console.log('🚫 Praxis payment cancelled');
      onCancel && onCancel();
    }
  };
}

/**
 * Initializes Praxis payment cashier
 * @param {Object} config - Praxis configuration
 * @returns {Promise} Resolves with cashier instance
 */
export async function initializePraxisPayment(config) {
  try {
    // Ensure SDK is loaded
    if (!window.PraxisCashier) {
      throw new Error('Praxis SDK not loaded');
    }
    
    // Create cashier instance
    const cashier = new window.PraxisCashier({
      merchantId: config.merchantId,
      environment: config.environment,
      
      // Payment configuration
      payment: {
        amount: config.amount,
        currency: config.currency,
        orderId: config.orderId,
        description: config.description,
        paymentMethod: config.paymentMethod
      },
      
      // Customer information
      customer: config.customer,
      
      // UI settings
      ui: {
        theme: config.theme,
        locale: config.locale,
        showPaymentMethods: true,
        showCustomerForm: false // Pre-filled from customer object
      },
      
      // Callbacks
      callbacks: {
        onSuccess: config.onSuccess,
        onFailure: config.onFailure,
        onPending: config.onPending,
        onCancel: config.onCancel
      },
      
      // URLs
      urls: {
        callback: config.callbackUrl,
        success: config.successUrl,
        failure: config.failureUrl
      }
    });
    
    return cashier;
    
  } catch (error) {
    console.error('❌ Error initializing Praxis payment:', error);
    throw error;
  }
}

/**
 * Direct API payment (fallback when SDK is not available)
 * @param {Object} paymentData - Payment data
 * @returns {Promise} Payment response
 */
export async function processPraxisDirectPayment(paymentData) {
  try {
    const apiUrl = PRAXIS_CONFIG.apiUrls[PRAXIS_CONFIG.environment];
    
    const response = await fetch(`${apiUrl}/payments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${PRAXIS_CONFIG.apiKey}`,
        'X-Merchant-ID': PRAXIS_CONFIG.merchantId
      },
      body: JSON.stringify({
        amount: paymentData.amount,
        currency: PRAXIS_CONFIG.currency,
        orderId: `direct_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        description: paymentData.description,
        paymentMethod: paymentData.paymentMethod,
        customer: paymentData.customer,
        callbackUrl: paymentData.callbackUrl
      })
    });
    
    if (!response.ok) {
      throw new Error(`Payment API error: ${response.status}`);
    }
    
    const result = await response.json();
    return result;
    
  } catch (error) {
    console.error('❌ Direct payment error:', error);
    throw error;
  }
}

/**
 * Get payment status
 * @param {string} transactionId - Transaction ID
 * @returns {Promise} Payment status
 */
export async function getPraxisPaymentStatus(transactionId) {
  try {
    const apiUrl = PRAXIS_CONFIG.apiUrls[PRAXIS_CONFIG.environment];
    
    const response = await fetch(`${apiUrl}/payments/${transactionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PRAXIS_CONFIG.apiKey}`,
        'X-Merchant-ID': PRAXIS_CONFIG.merchantId
      }
    });
    
    if (!response.ok) {
      throw new Error(`Status API error: ${response.status}`);
    }
    
    const result = await response.json();
    return result;
    
  } catch (error) {
    console.error('❌ Payment status error:', error);
    throw error;
  }
}

/**
 * Supported payment methods by region
 */
export const PRAXIS_PAYMENT_METHODS = {
  global: ['card', 'crypto', 'e_wallet'],
  europe: ['card', 'bank_transfer', 'e_wallet', 'voucher'],
  asia: ['card', 'crypto', 'e_wallet', 'bank_transfer'],
  americas: ['card', 'crypto', 'e_wallet', 'bank_transfer']
};

/**
 * Get available payment methods for user's region
 * @param {string} region - User's region
 * @returns {Array} Available payment methods
 */
export function getAvailablePaymentMethods(region = 'global') {
  return PRAXIS_PAYMENT_METHODS[region] || PRAXIS_PAYMENT_METHODS.global;
}

/**
 * Format amount for display
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @returns {string} Formatted amount
 */
export function formatPraxisAmount(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

/**
 * Validate payment data
 * @param {Object} paymentData - Payment data to validate
 * @returns {Object} Validation result
 */
export function validatePaymentData(paymentData) {
  const errors = [];
  
  if (!paymentData.amount || paymentData.amount <= 0) {
    errors.push('Amount must be greater than 0');
  }
  
  if (!paymentData.userId) {
    errors.push('User ID is required');
  }
  
  if (!paymentData.packageId) {
    errors.push('Package ID is required');
  }
  
  if (!paymentData.paymentMethod) {
    errors.push('Payment method is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

export default {
  validatePraxisConfig,
  loadPraxisSDK,
  createPraxisPaymentConfig,
  initializePraxisPayment,
  processPraxisDirectPayment,
  getPraxisPaymentStatus,
  getAvailablePaymentMethods,
  formatPraxisAmount,
  validatePaymentData,
  PRAXIS_PAYMENT_METHODS
};