// Praxis Payment Gateway Configuration

export const PRAXIS_CONFIG = {
  // Environment configuration
  environment: process.env.NEXT_PUBLIC_PRAXIS_ENVIRONMENT || 'sandbox',
  merchant_id: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_ID,
  api_key: process.env.NEXT_PUBLIC_PRAXIS_API_KEY,
  
  // SDK URL based on environment
  sdkUrl: process.env.NEXT_PUBLIC_PRAXIS_ENVIRONMENT === 'production' 
    ? 'https://cashier.praxiscashier.com/js/cashier.js'
    : 'https://cashier-sandbox.praxiscashier.com/js/cashier.js',
    
  // Default currency
  currency: 'USD',
  
  // Callback URLs
  getCallbackUrls: () => ({
    success_url: `${window.location.origin}/payment/success`,
    failure_url: `${window.location.origin}/payment/failure`,
    pending_url: `${window.location.origin}/payment/pending`,
    cancel_url: `${window.location.origin}/payment/cancel`,
  }),
};

// Generate unique order ID
export const generateOrderId = (packageId, userId) => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `pkg_${packageId}_${userId}_${timestamp}_${random}`;
};

// Validate Praxis configuration
export const validatePraxisConfig = () => {
  const errors = [];
  
  if (!PRAXIS_CONFIG.merchant_id) {
    errors.push('NEXT_PUBLIC_PRAXIS_MERCHANT_ID is not configured');
  }
  
  if (!PRAXIS_CONFIG.api_key) {
    errors.push('NEXT_PUBLIC_PRAXIS_API_KEY is not configured');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Create Praxis payment configuration
export const createPraxisPaymentConfig = ({
  packageData,
  userDetails,
  onSuccess,
  onFailure,
  onPending,
  onCancel,
}) => {
  const orderId = generateOrderId(packageData.packageId, userDetails.userId);
  
  return {
    // Merchant configuration
    merchant_id: PRAXIS_CONFIG.merchant_id,
    api_key: PRAXIS_CONFIG.api_key,
    environment: PRAXIS_CONFIG.environment,
    
    // Transaction details
    amount: packageData.amount,
    currency: PRAXIS_CONFIG.currency,
    order_id: orderId,
    
    // Customer information
    customer: {
      id: userDetails.userId.toString(),
      email: userDetails.email || '',
      first_name: userDetails.firstName || userDetails.username || '',
      last_name: userDetails.lastName || '',
      phone: userDetails.phone || '',
    },
    
    // Product information
    description: `${packageData.name} - ${packageData.features.join(', ')}`,
    
    // Callback URLs
    ...PRAXIS_CONFIG.getCallbackUrls(),
    
    // Event handlers
    onSuccess,
    onFailure,
    onPending,
    onCancel,
    
    // Additional options
    theme: {
      primary_color: '#f59e0b', // Yellow color matching your design
      background_color: '#1f2937', // Dark background
      text_color: '#ffffff',
    },
    
    // Payment methods (optional - let Praxis decide based on merchant config)
    payment_methods: ['card', 'bank_transfer', 'crypto'],
    
    // Locale
    locale: 'en',
  };
};

// Load Praxis SDK
export const loadPraxisSDK = () => {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    if (window.PraxisCashier) {
      resolve(window.PraxisCashier);
      return;
    }

    // Check if script is already being loaded
    const existingScript = document.querySelector(`script[src="${PRAXIS_CONFIG.sdkUrl}"]`);
    if (existingScript) {
      existingScript.addEventListener('load', () => resolve(window.PraxisCashier));
      existingScript.addEventListener('error', reject);
      return;
    }

    // Create and load script
    const script = document.createElement('script');
    script.src = PRAXIS_CONFIG.sdkUrl;
    script.async = true;
    
    script.onload = () => {
      if (window.PraxisCashier) {
        resolve(window.PraxisCashier);
      } else {
        reject(new Error('PraxisCashier not found after script load'));
      }
    };
    
    script.onerror = () => {
      reject(new Error('Failed to load Praxis SDK'));
    };
    
    document.head.appendChild(script);
  });
};

// Initialize Praxis payment
export const initializePraxisPayment = async (config) => {
  try {
    // Validate configuration
    const validation = validatePraxisConfig();
    if (!validation.isValid) {
      throw new Error(`Praxis configuration error: ${validation.errors.join(', ')}`);
    }

    // Load SDK
    const PraxisCashier = await loadPraxisSDK();
    
    // Create cashier instance
    const cashier = new PraxisCashier(config);
    
    return cashier;
  } catch (error) {
    console.error('Error initializing Praxis payment:', error);
    throw error;
  }
};
