// Praxis Payment Gateway Configuration

export const PRAXIS_CONFIG = {
  // Environment configuration
  environment: process.env.NEXT_PUBLIC_PRAXIS_ENVIRONMENT || 'sandbox',
  merchant_id: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_ID,
  api_key: process.env.NEXT_PUBLIC_PRAXIS_API_KEY,

  // SDK URLs with fallbacks
  sdkUrls: process.env.NEXT_PUBLIC_PRAXIS_ENVIRONMENT === 'production'
    ? [
        'https://cashier.praxiscashier.com/assets/js/cashier.min.js',
        'https://cashier.praxiscashier.com/js/cashier.js',
        'https://cdn.praxiscashier.com/js/cashier.min.js'
      ]
    : [
        'https://sandbox-cashier.praxiscashier.com/assets/js/cashier.min.js',
        'https://sandbox-cashier.praxiscashier.com/js/cashier.js',
        'https://cashier-sandbox.praxiscashier.com/js/cashier.js',
        'https://demo-cashier.praxiscashier.com/js/cashier.js'
      ],

  // Primary SDK URL (for backward compatibility)
  get sdkUrl() {
    return this.sdkUrls[0];
  },

  // Default currency
  currency: 'USD',

  // Callback URLs
  getCallbackUrls: () => ({
    success_url: `${window.location.origin}/payment/success`,
    failure_url: `${window.location.origin}/payment/failure`,
    pending_url: `${window.location.origin}/payment/pending`,
    cancel_url: `${window.location.origin}/payment/cancel`,
  }),
};

// Test if any Praxis SDK URL is accessible
export const testPraxisSDKUrl = async () => {
  console.log('🌐 Testing Praxis SDK URLs accessibility...');

  for (let i = 0; i < PRAXIS_CONFIG.sdkUrls.length; i++) {
    const url = PRAXIS_CONFIG.sdkUrls[i];
    try {
      console.log(`📡 Testing URL ${i + 1}/${PRAXIS_CONFIG.sdkUrls.length}:`, url);
      const response = await fetch(url, { method: 'HEAD' });
      console.log(`� URL ${i + 1} response status:`, response.status);

      if (response.ok) {
        console.log(`✅ Found working SDK URL: ${url}`);
        return true;
      }
    } catch (error) {
      console.warn(`⚠️ URL ${i + 1} test failed:`, error.message);
    }
  }

  console.error('❌ All SDK URLs failed accessibility test');
  return false;
};

// Generate unique order ID
export const generateOrderId = (packageId, userId) => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `pkg_${packageId}_${userId}_${timestamp}_${random}`;
};

// Validate Praxis configuration
export const validatePraxisConfig = () => {
  console.log('🔍 Validating Praxis configuration...');
  console.log('📋 Current config:', {
    merchant_id: PRAXIS_CONFIG.merchant_id ? '***configured***' : 'NOT SET',
    api_key: PRAXIS_CONFIG.api_key ? '***configured***' : 'NOT SET',
    environment: PRAXIS_CONFIG.environment,
    sdkUrl: PRAXIS_CONFIG.sdkUrl,
  });

  const errors = [];

  if (!PRAXIS_CONFIG.merchant_id) {
    errors.push('NEXT_PUBLIC_PRAXIS_MERCHANT_ID is not configured');
  }

  if (!PRAXIS_CONFIG.api_key) {
    errors.push('NEXT_PUBLIC_PRAXIS_API_KEY is not configured');
  }

  const result = {
    isValid: errors.length === 0,
    errors,
  };

  if (result.isValid) {
    console.log('✅ Praxis configuration is valid');
  } else {
    console.error('❌ Praxis configuration errors:', errors);
  }

  return result;
};

// Create Praxis payment configuration
export const createPraxisPaymentConfig = ({
  packageData,
  userDetails,
  onSuccess,
  onFailure,
  onPending,
  onCancel,
}) => {
  const orderId = generateOrderId(packageData.packageId, userDetails.userId);
  
  return {
    // Merchant configuration
    merchant_id: PRAXIS_CONFIG.merchant_id,
    api_key: PRAXIS_CONFIG.api_key,
    environment: PRAXIS_CONFIG.environment,
    
    // Transaction details
    amount: packageData.amount,
    currency: PRAXIS_CONFIG.currency,
    order_id: orderId,
    
    // Customer information
    customer: {
      id: userDetails.userId.toString(),
      email: userDetails.email || '',
      first_name: userDetails.firstName || userDetails.username || '',
      last_name: userDetails.lastName || '',
      phone: userDetails.phone || '',
    },
    
    // Product information
    description: `${packageData.name} - ${packageData.features.join(', ')}`,
    
    // Callback URLs
    ...PRAXIS_CONFIG.getCallbackUrls(),
    
    // Event handlers
    onSuccess,
    onFailure,
    onPending,
    onCancel,
    
    // Additional options
    theme: {
      primary_color: '#f59e0b', // Yellow color matching your design
      background_color: '#1f2937', // Dark background
      text_color: '#ffffff',
    },
    
    // Payment methods (optional - let Praxis decide based on merchant config)
    payment_methods: ['card', 'bank_transfer', 'crypto'],
    
    // Locale
    locale: 'en',
  };
};

// Load Praxis SDK with multiple URL fallbacks
export const loadPraxisSDK = () => {
  return new Promise(async (resolve, reject) => {
    console.log('🔄 Loading Praxis SDK with fallback URLs...');

    // Check if already loaded
    if (window.PraxisCashier) {
      console.log('✅ Praxis SDK already loaded');
      resolve(window.PraxisCashier);
      return;
    }

    // Try each URL until one works
    for (let i = 0; i < PRAXIS_CONFIG.sdkUrls.length; i++) {
      const sdkUrl = PRAXIS_CONFIG.sdkUrls[i];
      console.log(`🔄 Trying SDK URL ${i + 1}/${PRAXIS_CONFIG.sdkUrls.length}:`, sdkUrl);

      try {
        // Test URL accessibility first
        const response = await fetch(sdkUrl, { method: 'HEAD' });
        if (!response.ok) {
          console.warn(`⚠️ SDK URL ${i + 1} not accessible (${response.status}), trying next...`);
          continue;
        }

        // Check if script is already being loaded
        const existingScript = document.querySelector(`script[src="${sdkUrl}"]`);
        if (existingScript) {
          console.log('⏳ Praxis SDK script already exists, waiting for load...');
          existingScript.addEventListener('load', () => {
            console.log('✅ Existing Praxis SDK script loaded');
            resolve(window.PraxisCashier);
          });
          existingScript.addEventListener('error', (error) => {
            console.error('❌ Existing Praxis SDK script failed to load:', error);
            reject(error);
          });
          return;
        }

        // Try to load the script
        const success = await new Promise((scriptResolve, scriptReject) => {
          const script = document.createElement('script');
          script.src = sdkUrl;
          script.async = true;

          script.onload = () => {
            console.log(`📦 SDK script loaded from URL ${i + 1}, checking for PraxisCashier...`);
            if (window.PraxisCashier) {
              console.log('✅ PraxisCashier found on window object');
              scriptResolve(true);
            } else {
              console.error('❌ PraxisCashier not found on window object after script load');
              console.log('🔍 Available window properties:', Object.keys(window).filter(key => key.toLowerCase().includes('praxis')));
              scriptReject(new Error('PraxisCashier not found after script load'));
            }
          };

          script.onerror = (error) => {
            console.error(`❌ Failed to load SDK from URL ${i + 1}:`, error);
            scriptReject(error);
          };

          console.log(`📥 Adding SDK script ${i + 1} to document head...`);
          document.head.appendChild(script);
        });

        if (success) {
          resolve(window.PraxisCashier);
          return;
        }
      } catch (error) {
        console.warn(`⚠️ SDK URL ${i + 1} failed:`, error.message);
        if (i === PRAXIS_CONFIG.sdkUrls.length - 1) {
          // Last URL failed
          console.error('❌ All SDK URLs failed to load');
          reject(new Error('Failed to load Praxis SDK from any URL'));
        }
      }
    }
  });
};

// Mock Praxis Cashier for demo/testing purposes
class MockPraxisCashier {
  constructor(config) {
    this.config = config;
    console.log('🎭 Mock Praxis Cashier initialized with config:', config);
  }

  open() {
    console.log('🎭 Mock Praxis payment modal opening...');

    // Simulate payment modal with a simple confirm dialog
    setTimeout(() => {
      const userChoice = window.confirm(
        `Mock Payment Demo\n\n` +
        `Amount: $${this.config.amount}\n` +
        `Description: ${this.config.description}\n\n` +
        `Click OK to simulate successful payment, Cancel to simulate failure.`
      );

      if (userChoice) {
        // Simulate successful payment
        console.log('🎭 Mock payment successful');
        if (this.config.onSuccess) {
          this.config.onSuccess({
            transaction_id: `mock_txn_${Date.now()}`,
            order_id: this.config.order_id,
            amount: this.config.amount,
            status: 'completed',
            payment_method: 'mock_card',
          });
        }
      } else {
        // Simulate payment failure
        console.log('🎭 Mock payment cancelled/failed');
        if (this.config.onCancel) {
          this.config.onCancel();
        }
      }
    }, 1000); // 1 second delay to simulate loading
  }

  close() {
    console.log('🎭 Mock Praxis cashier closed');
  }
}

// Initialize Praxis payment
export const initializePraxisPayment = async (config) => {
  try {
    // Validate configuration
    const validation = validatePraxisConfig();
    if (!validation.isValid) {
      console.warn('⚠️ Praxis configuration invalid, using mock payment');
      return new MockPraxisCashier(config);
    }

    // Try to load real SDK
    try {
      const PraxisCashier = await loadPraxisSDK();
      console.log('✅ Using real Praxis SDK');
      return new PraxisCashier(config);
    } catch (sdkError) {
      console.warn('⚠️ Real Praxis SDK failed, using mock payment:', sdkError.message);
      return new MockPraxisCashier(config);
    }
  } catch (error) {
    console.error('❌ Error initializing Praxis payment:', error);
    console.log('🎭 Falling back to mock payment');
    return new MockPraxisCashier(config);
  }
};
