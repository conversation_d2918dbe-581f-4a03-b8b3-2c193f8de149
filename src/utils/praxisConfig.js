// utils/praxisConfig.js
// Official Praxis Payment Gateway Integration
// Based on: https://doc.praxiscashier.com/integration_docs/latest/demo/cashier_test_page

/**
 * Praxis configuration for Six Sides Global / FansBets
 */
const PRAXIS_CONFIG = {
  // Six Sides Global credentials
  merchantId: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_ID || 'API-FansbetsUS',
  merchantSecret: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_SECRET || '39BTRoOcVpyL24mbi7Idkmu6Sbc3bcmC',
  applicationKey: process.env.NEXT_PUBLIC_PRAXIS_APPLICATION_KEY || 'fansbets.us',
  environment: process.env.NEXT_PUBLIC_PRAXIS_ENVIRONMENT || 'demo', // 'demo' or 'production'
  
  // Official Praxis SDK URLs from documentation
  sdkUrls: {
    demo: 'https://demo.praxiscashier.com/assets/js/cashier.min.js',
    production: 'https://cashier.praxiscashier.com/assets/js/cashier.min.js'
  },
  
  // Default configuration
  currency: 'USD',
  locale: 'en',
  theme: 'dark'
};

/**
 * Validate Praxis configuration
 */
export function validatePraxisConfig() {
  console.log('🔍 Validating Praxis configuration...');
  
  const errors = [];
  
  if (!PRAXIS_CONFIG.merchantId || PRAXIS_CONFIG.merchantId === 'your_merchant_id') {
    errors.push('Merchant ID is required');
  }
  
  if (!PRAXIS_CONFIG.merchantSecret || PRAXIS_CONFIG.merchantSecret === 'your_merchant_secret') {
    errors.push('Merchant Secret is required');
  }
  
  if (!PRAXIS_CONFIG.applicationKey || PRAXIS_CONFIG.applicationKey === 'your_application_key') {
    errors.push('Application Key is required');
  }
  
  const result = {
    isValid: errors.length === 0,
    errors,
    config: PRAXIS_CONFIG
  };
  
  if (result.isValid) {
    console.log('✅ Praxis configuration is valid');
    console.log('🏪 Merchant ID:', PRAXIS_CONFIG.merchantId);
    console.log('🔑 Application Key:', PRAXIS_CONFIG.applicationKey);
    console.log('🌍 Environment:', PRAXIS_CONFIG.environment);
  } else {
    console.error('❌ Praxis configuration errors:', errors);
  }
  
  return result;
}

/**
 * Load Praxis SDK from official documentation
 */
export function loadPraxisSDK() {
  return new Promise((resolve, reject) => {
    console.log('🔄 Loading Praxis SDK...');
    
    // Check if SDK is already loaded
    if (window.PraxisCashier) {
      console.log('✅ Praxis SDK already loaded');
      resolve(window.PraxisCashier);
      return;
    }
    
    // Get SDK URL for current environment
    const sdkUrl = PRAXIS_CONFIG.sdkUrls[PRAXIS_CONFIG.environment];
    console.log(`📥 Loading SDK from: ${sdkUrl}`);
    
    // Create script element
    const script = document.createElement('script');
    script.src = sdkUrl;
    script.async = true;
    
    // Set timeout
    const timeout = setTimeout(() => {
      console.error('⏰ SDK loading timed out');
      reject(new Error('SDK loading timeout'));
    }, 15000);
    
    script.onload = () => {
      clearTimeout(timeout);
      if (window.PraxisCashier) {
        console.log('✅ Praxis SDK loaded successfully');
        resolve(window.PraxisCashier);
      } else {
        console.error('❌ SDK loaded but PraxisCashier not found');
        reject(new Error('PraxisCashier not found after script load'));
      }
    };
    
    script.onerror = (error) => {
      clearTimeout(timeout);
      console.error('❌ Failed to load Praxis SDK:', error);
      reject(new Error('Failed to load Praxis SDK'));
    };
    
    // Add script to document
    document.head.appendChild(script);
  });
}

/**
 * Create Praxis payment configuration based on official documentation
 */
export function createPraxisPaymentConfig({
  packageData,
  userDetails,
  paymentMethod = 'card',
  onSuccess,
  onFailure,
  onPending,
  onCancel
}) {
  const orderId = `order_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  return {
    // Merchant credentials (required by Praxis)
    merchant_id: PRAXIS_CONFIG.merchantId,
    application_key: PRAXIS_CONFIG.applicationKey,
    
    // Transaction details
    amount: packageData.amount,
    currency: PRAXIS_CONFIG.currency,
    order_id: orderId,
    description: `${packageData.name} - FansBets Package`,
    
    // Customer details
    customer: {
      id: userDetails.userId,
      email: userDetails.email || `user${userDetails.userId}@fansbets.us`,
      first_name: userDetails.firstName || userDetails.username || 'User',
      last_name: userDetails.lastName || '',
      phone: userDetails.phone || '',
      country: userDetails.country || 'US'
    },
    
    // Payment method
    payment_method: paymentMethod,
    
    // Callback URLs (for production)
    success_url: `${window.location.origin}/payment/success`,
    failure_url: `${window.location.origin}/payment/failure`,
    pending_url: `${window.location.origin}/payment/pending`,
    cancel_url: `${window.location.origin}/payment/cancel`,
    
    // Callback functions
    onSuccess: onSuccess,
    onFailure: onFailure,
    onPending: onPending,
    onCancel: onCancel,
    
    // UI configuration
    theme: PRAXIS_CONFIG.theme,
    locale: PRAXIS_CONFIG.locale
  };
}

/**
 * Initialize Praxis payment based on official documentation
 */
export async function initializePraxisPayment(config) {
  console.log('🚀 Initializing Praxis payment...');
  
  if (!window.PraxisCashier) {
    throw new Error('Praxis SDK not loaded');
  }
  
  try {
    // Create Praxis cashier instance
    const cashier = new window.PraxisCashier(config);
    console.log('✅ Praxis cashier created successfully');
    return cashier;
  } catch (error) {
    console.error('❌ Failed to create Praxis cashier:', error);
    throw error;
  }
}

/**
 * Test Praxis credentials and configuration
 */
export function testPraxisCredentials() {
  console.log('🧪 Testing Praxis credentials...');
  
  const validation = validatePraxisConfig();
  
  if (validation.isValid) {
    console.log('✅ Credentials loaded successfully:');
    console.log('   🏢 Company: Six Sides Global');
    console.log('   📍 Address: 733 Hunkins Waterfront Jewels, Charlestown, Nevis, KN0802');
    console.log('   🏪 Merchant ID: API-FansbetsUS');
    console.log('   🔑 Application Key: fansbets.us');
    console.log('   🌍 Environment: demo');
    console.log('   🚀 Ready for payment processing!');
    return true;
  } else {
    console.error('❌ Credential validation failed:', validation.errors);
    return false;
  }
}

/**
 * Debug function for browser console
 */
if (typeof window !== 'undefined') {
  window.debugPraxis = async function() {
    console.log('🔧 PRAXIS DEBUG MODE 🔧');
    console.log('========================');
    
    // Test credentials
    console.log('1. Testing credentials...');
    const credentialsValid = testPraxisCredentials();
    
    // Test configuration
    console.log('2. Testing configuration...');
    const configValid = validatePraxisConfig();
    
    // Test SDK loading
    console.log('3. Testing SDK loading...');
    try {
      const SDK = await loadPraxisSDK();
      console.log('✅ SDK loading test successful:', SDK);
    } catch (error) {
      console.error('❌ SDK loading test failed:', error);
    }
    
    console.log('========================');
    console.log('🔧 DEBUG COMPLETE 🔧');
    
    return {
      credentials: credentialsValid,
      config: configValid
    };
  };
}

export default PRAXIS_CONFIG;
