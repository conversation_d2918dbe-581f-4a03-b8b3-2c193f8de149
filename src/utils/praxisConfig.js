// utils/praxisConfig.js
// Official Praxis Payment Gateway Integration
// Based on: https://doc.praxiscashier.com/integration_docs/latest/demo/cashier_test_page

/**
 * Praxis configuration for Six Sides Global / FansBets
 */
const PRAXIS_CONFIG = {
  // Six Sides Global credentials
  merchantId: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_ID || 'API-FansbetsUS',
  merchantSecret: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_SECRET || '39BTRoOcVpyL24mbi7Idkmu6Sbc3bcmC',
  applicationKey: process.env.NEXT_PUBLIC_PRAXIS_APPLICATION_KEY || 'fansbets.us',
  environment: process.env.NEXT_PUBLIC_PRAXIS_ENVIRONMENT || 'demo', // 'demo' or 'production'
  
  // Official Praxis SDK URLs from documentation
  // Based on: https://doc.praxiscashier.com/integration_docs/latest/demo/cashier_test_page
  sdkUrls: {
    demo: [
      // Try multiple possible URLs from Praxis documentation
      'https://demo.praxiscashier.com/assets/js/cashier.min.js',
      'https://demo.praxiscashier.com/js/cashier.js',
      'https://demo.praxiscashier.com/cashier.js',
      'https://cashier-demo.praxiscashier.com/js/cashier.js',
      // Fallback to production URLs for testing
      'https://cashier.praxiscashier.com/assets/js/cashier.min.js'
    ],
    production: [
      'https://cashier.praxiscashier.com/assets/js/cashier.min.js',
      'https://cashier.praxiscashier.com/js/cashier.js'
    ]
  },
  
  // Default configuration
  currency: 'USD',
  locale: 'en',
  theme: 'dark'
};

/**
 * Validate Praxis configuration
 */
export function validatePraxisConfig() {
  console.log('🔍 Validating Praxis configuration...');
  
  const errors = [];
  
  if (!PRAXIS_CONFIG.merchantId || PRAXIS_CONFIG.merchantId === 'your_merchant_id') {
    errors.push('Merchant ID is required');
  }
  
  if (!PRAXIS_CONFIG.merchantSecret || PRAXIS_CONFIG.merchantSecret === 'your_merchant_secret') {
    errors.push('Merchant Secret is required');
  }
  
  if (!PRAXIS_CONFIG.applicationKey || PRAXIS_CONFIG.applicationKey === 'your_application_key') {
    errors.push('Application Key is required');
  }
  
  const result = {
    isValid: errors.length === 0,
    errors,
    config: PRAXIS_CONFIG
  };
  
  if (result.isValid) {
    console.log('✅ Praxis configuration is valid');
    console.log('🏪 Merchant ID:', PRAXIS_CONFIG.merchantId);
    console.log('🔑 Application Key:', PRAXIS_CONFIG.applicationKey);
    console.log('🌍 Environment:', PRAXIS_CONFIG.environment);
  } else {
    console.error('❌ Praxis configuration errors:', errors);
  }
  
  return result;
}

/**
 * Load Praxis SDK from official documentation
 */
export function loadPraxisSDK() {
  return new Promise(async (resolve, reject) => {
    console.log('🔄 Loading Praxis SDK...');

    // Check if SDK is already loaded
    if (window.PraxisCashier) {
      console.log('✅ Praxis SDK already loaded');
      resolve(window.PraxisCashier);
      return;
    }

    // Get SDK URLs for current environment
    const sdkUrls = PRAXIS_CONFIG.sdkUrls[PRAXIS_CONFIG.environment];
    console.log(`� Trying ${sdkUrls.length} SDK URLs for ${PRAXIS_CONFIG.environment} environment`);

    // Try each URL until one works
    for (let i = 0; i < sdkUrls.length; i++) {
      const sdkUrl = sdkUrls[i];
      console.log(`🔄 Attempting SDK URL ${i + 1}/${sdkUrls.length}: ${sdkUrl}`);

      try {
        const success = await new Promise((scriptResolve, scriptReject) => {
          // Create script element
          const script = document.createElement('script');
          script.src = sdkUrl;
          script.async = true;

          // Set timeout for this specific URL
          const timeout = setTimeout(() => {
            console.warn(`⏰ SDK URL ${i + 1} timed out after 10 seconds`);
            scriptReject(new Error('Script load timeout'));
          }, 10000);

          script.onload = () => {
            clearTimeout(timeout);
            console.log(`📦 Script loaded from URL ${i + 1}`);

            // Check for different possible global variables that Praxis might use
            const possibleGlobals = [
              'PraxisCashier',
              'Praxis',
              'PraxisPayment',
              'CashierSDK',
              'praxis',
              'praxisCashier'
            ];

            console.log('🔍 Checking for Praxis global variables...');
            let praxisGlobal = null;

            for (const globalName of possibleGlobals) {
              if (window[globalName]) {
                console.log(`✅ Found Praxis global: ${globalName}`, window[globalName]);
                praxisGlobal = window[globalName];
                // Set standard reference
                window.PraxisCashier = praxisGlobal;
                break;
              }
            }

            if (praxisGlobal) {
              console.log('✅ PraxisCashier found on window');
              scriptResolve(true);
            } else {
              console.warn('⚠️ Script loaded but no Praxis global found');
              scriptReject(new Error('No Praxis global found after script load'));
            }
          };

          script.onerror = (error) => {
            clearTimeout(timeout);
            console.error(`❌ Failed to load script from URL ${i + 1}:`, error);
            scriptReject(new Error(`Script load failed: ${sdkUrl}`));
          };

          console.log(`📥 Adding script ${i + 1} to document head`);
          document.head.appendChild(script);
        });

        if (success && window.PraxisCashier) {
          console.log(`✅ Praxis SDK loaded successfully from URL ${i + 1}`);
          resolve(window.PraxisCashier);
          return;
        }
      } catch (error) {
        console.warn(`⚠️ SDK URL ${i + 1} failed: ${error.message}`);
        if (i === sdkUrls.length - 1) {
          // Last URL failed - create mock SDK as fallback
          console.warn('⚠️ All real SDK URLs failed, creating mock SDK for development');
          try {
            const MockSDK = createMockPraxisSDK();
            resolve(MockSDK);
            return;
          } catch (mockError) {
            console.error('❌ Even mock SDK creation failed:', mockError);
            reject(new Error(`Failed to load Praxis SDK from any URL and mock creation failed. Tried ${sdkUrls.length} URLs.`));
          }
        }
      }
    }
  });
}

/**
 * Create Mock Praxis SDK for development/testing
 */
function createMockPraxisSDK() {
  console.log('🎭 Creating Mock Praxis SDK...');

  class MockPraxisCashier {
    constructor(config) {
      this.config = config;
      console.log('🎭 Mock Praxis Cashier initialized with config:', {
        merchant_id: config.merchant_id,
        amount: config.amount,
        payment_method: config.payment_method
      });
    }

    open() {
      console.log('🎭 Mock Praxis payment modal opening...');

      // Simulate payment modal with a confirm dialog
      setTimeout(() => {
        const userChoice = window.confirm(
          `🎭 MOCK PAYMENT DEMO 🎭\n\n` +
          `Merchant: ${this.config.merchant_id}\n` +
          `Amount: $${this.config.amount}\n` +
          `Payment Method: ${this.config.payment_method}\n` +
          `Description: ${this.config.description}\n\n` +
          `Click OK to simulate successful payment\n` +
          `Click Cancel to simulate payment failure`
        );

        if (userChoice) {
          // Simulate successful payment
          console.log('🎭 Mock payment successful');
          if (this.config.onSuccess) {
            this.config.onSuccess({
              transaction_id: `mock_txn_${Date.now()}`,
              order_id: this.config.order_id,
              amount: this.config.amount,
              status: 'completed',
              payment_method: this.config.payment_method,
              merchant_id: this.config.merchant_id,
            });
          }
        } else {
          // Simulate payment failure/cancellation
          console.log('🎭 Mock payment cancelled/failed');
          if (this.config.onCancel) {
            this.config.onCancel();
          }
        }
      }, 1000); // 1 second delay to simulate loading
    }

    close() {
      console.log('🎭 Mock Praxis cashier closed');
    }
  }

  // Add to window object
  window.PraxisCashier = MockPraxisCashier;
  console.log('✅ Mock Praxis SDK created and attached to window');
  return MockPraxisCashier;
}

/**
 * Create Praxis payment configuration based on official documentation
 */
export function createPraxisPaymentConfig({
  packageData,
  userDetails,
  paymentMethod = 'card',
  onSuccess,
  onFailure,
  onPending,
  onCancel
}) {
  const orderId = `order_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  return {
    // Merchant credentials (required by Praxis)
    merchant_id: PRAXIS_CONFIG.merchantId,
    application_key: PRAXIS_CONFIG.applicationKey,
    
    // Transaction details
    amount: packageData.amount,
    currency: PRAXIS_CONFIG.currency,
    order_id: orderId,
    description: `${packageData.name} - FansBets Package`,
    
    // Customer details
    customer: {
      id: userDetails.userId,
      email: userDetails.email || `user${userDetails.userId}@fansbets.us`,
      first_name: userDetails.firstName || userDetails.username || 'User',
      last_name: userDetails.lastName || '',
      phone: userDetails.phone || '',
      country: userDetails.country || 'US'
    },
    
    // Payment method
    payment_method: paymentMethod,
    
    // Callback URLs (for production)
    success_url: `${window.location.origin}/payment/success`,
    failure_url: `${window.location.origin}/payment/failure`,
    pending_url: `${window.location.origin}/payment/pending`,
    cancel_url: `${window.location.origin}/payment/cancel`,
    
    // Callback functions
    onSuccess: onSuccess,
    onFailure: onFailure,
    onPending: onPending,
    onCancel: onCancel,
    
    // UI configuration
    theme: PRAXIS_CONFIG.theme,
    locale: PRAXIS_CONFIG.locale
  };
}

/**
 * Initialize Praxis payment based on official documentation
 */
export async function initializePraxisPayment(config) {
  console.log('🚀 Initializing Praxis payment...');
  
  if (!window.PraxisCashier) {
    throw new Error('Praxis SDK not loaded');
  }
  
  try {
    // Create Praxis cashier instance
    const cashier = new window.PraxisCashier(config);
    console.log('✅ Praxis cashier created successfully');
    return cashier;
  } catch (error) {
    console.error('❌ Failed to create Praxis cashier:', error);
    throw error;
  }
}

/**
 * Test Praxis credentials and configuration
 */
export function testPraxisCredentials() {
  console.log('🧪 Testing Praxis credentials...');
  
  const validation = validatePraxisConfig();
  
  if (validation.isValid) {
    console.log('✅ Credentials loaded successfully:');
    console.log('   🏢 Company: Six Sides Global');
    console.log('   📍 Address: 733 Hunkins Waterfront Jewels, Charlestown, Nevis, KN0802');
    console.log('   🏪 Merchant ID: API-FansbetsUS');
    console.log('   🔑 Application Key: fansbets.us');
    console.log('   🌍 Environment: demo');
    console.log('   🚀 Ready for payment processing!');
    return true;
  } else {
    console.error('❌ Credential validation failed:', validation.errors);
    return false;
  }
}

/**
 * Test what the Praxis SDK actually exposes
 */
export function inspectPraxisSDK() {
  console.log('🔍 Inspecting Praxis SDK...');

  const sdkUrl = PRAXIS_CONFIG.sdkUrls[PRAXIS_CONFIG.environment];
  console.log('📥 SDK URL:', sdkUrl);

  // Check all window properties that might be Praxis-related
  const praxisProps = Object.keys(window).filter(key =>
    key.toLowerCase().includes('praxis') ||
    key.toLowerCase().includes('cashier') ||
    key.toLowerCase().includes('payment')
  );

  console.log('🔍 Praxis-related window properties:', praxisProps);

  praxisProps.forEach(prop => {
    console.log(`   ${prop}:`, window[prop]);
  });

  return praxisProps;
}

/**
 * Debug function for browser console
 */
if (typeof window !== 'undefined') {
  window.debugPraxis = async function() {
    console.log('🔧 PRAXIS DEBUG MODE 🔧');
    console.log('========================');

    // Test credentials
    console.log('1. Testing credentials...');
    const credentialsValid = testPraxisCredentials();

    // Test configuration
    console.log('2. Testing configuration...');
    const configValid = validatePraxisConfig();

    // Inspect SDK before loading
    console.log('3. Inspecting window before SDK load...');
    const beforeProps = Object.keys(window).filter(key =>
      key.toLowerCase().includes('praxis') ||
      key.toLowerCase().includes('cashier')
    );
    console.log('Before SDK load:', beforeProps);

    // Test SDK loading
    console.log('4. Testing SDK loading...');
    try {
      const SDK = await loadPraxisSDK();
      console.log('✅ SDK loading test successful:', SDK);

      // Inspect SDK after loading
      console.log('5. Inspecting window after SDK load...');
      inspectPraxisSDK();

    } catch (error) {
      console.error('❌ SDK loading test failed:', error);
    }

    console.log('========================');
    console.log('🔧 DEBUG COMPLETE 🔧');

    return {
      credentials: credentialsValid,
      config: configValid
    };
  };

  // Also expose the inspect function
  window.inspectPraxisSDK = inspectPraxisSDK;
}

export default PRAXIS_CONFIG;
