// Praxis Payment Gateway Configuration

export const PRAXIS_CONFIG = {
  // Environment configuration
  environment: process.env.NEXT_PUBLIC_PRAXIS_ENVIRONMENT || 'sandbox',
  merchant_id: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_ID,
  api_key: process.env.NEXT_PUBLIC_PRAXIS_API_KEY,

  // SDK URL based on environment
  sdkUrl: process.env.NEXT_PUBLIC_PRAXIS_ENVIRONMENT === 'production'
    ? 'https://cashier.praxiscashier.com/js/cashier.js'
    : 'https://cashier-sandbox.praxiscashier.com/js/cashier.js',

  // Default currency
  currency: 'USD',

  // Callback URLs
  getCallbackUrls: () => ({
    success_url: `${window.location.origin}/payment/success`,
    failure_url: `${window.location.origin}/payment/failure`,
    pending_url: `${window.location.origin}/payment/pending`,
    cancel_url: `${window.location.origin}/payment/cancel`,
  }),
};

// Test if Praxis SDK URL is accessible
export const testPraxisSDKUrl = async () => {
  try {
    console.log('🌐 Testing Praxis SDK URL accessibility:', PRAXIS_CONFIG.sdkUrl);
    const response = await fetch(PRAXIS_CONFIG.sdkUrl, { method: 'HEAD' });
    console.log('📡 SDK URL response status:', response.status);
    return response.ok;
  } catch (error) {
    console.error('❌ SDK URL test failed:', error);
    return false;
  }
};

// Generate unique order ID
export const generateOrderId = (packageId, userId) => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `pkg_${packageId}_${userId}_${timestamp}_${random}`;
};

// Validate Praxis configuration
export const validatePraxisConfig = () => {
  console.log('🔍 Validating Praxis configuration...');
  console.log('📋 Current config:', {
    merchant_id: PRAXIS_CONFIG.merchant_id ? '***configured***' : 'NOT SET',
    api_key: PRAXIS_CONFIG.api_key ? '***configured***' : 'NOT SET',
    environment: PRAXIS_CONFIG.environment,
    sdkUrl: PRAXIS_CONFIG.sdkUrl,
  });

  const errors = [];

  if (!PRAXIS_CONFIG.merchant_id) {
    errors.push('NEXT_PUBLIC_PRAXIS_MERCHANT_ID is not configured');
  }

  if (!PRAXIS_CONFIG.api_key) {
    errors.push('NEXT_PUBLIC_PRAXIS_API_KEY is not configured');
  }

  const result = {
    isValid: errors.length === 0,
    errors,
  };

  if (result.isValid) {
    console.log('✅ Praxis configuration is valid');
  } else {
    console.error('❌ Praxis configuration errors:', errors);
  }

  return result;
};

// Create Praxis payment configuration
export const createPraxisPaymentConfig = ({
  packageData,
  userDetails,
  onSuccess,
  onFailure,
  onPending,
  onCancel,
}) => {
  const orderId = generateOrderId(packageData.packageId, userDetails.userId);
  
  return {
    // Merchant configuration
    merchant_id: PRAXIS_CONFIG.merchant_id,
    api_key: PRAXIS_CONFIG.api_key,
    environment: PRAXIS_CONFIG.environment,
    
    // Transaction details
    amount: packageData.amount,
    currency: PRAXIS_CONFIG.currency,
    order_id: orderId,
    
    // Customer information
    customer: {
      id: userDetails.userId.toString(),
      email: userDetails.email || '',
      first_name: userDetails.firstName || userDetails.username || '',
      last_name: userDetails.lastName || '',
      phone: userDetails.phone || '',
    },
    
    // Product information
    description: `${packageData.name} - ${packageData.features.join(', ')}`,
    
    // Callback URLs
    ...PRAXIS_CONFIG.getCallbackUrls(),
    
    // Event handlers
    onSuccess,
    onFailure,
    onPending,
    onCancel,
    
    // Additional options
    theme: {
      primary_color: '#f59e0b', // Yellow color matching your design
      background_color: '#1f2937', // Dark background
      text_color: '#ffffff',
    },
    
    // Payment methods (optional - let Praxis decide based on merchant config)
    payment_methods: ['card', 'bank_transfer', 'crypto'],
    
    // Locale
    locale: 'en',
  };
};

// Load Praxis SDK
export const loadPraxisSDK = () => {
  return new Promise((resolve, reject) => {
    console.log('🔄 Loading Praxis SDK from:', PRAXIS_CONFIG.sdkUrl);

    // Check if already loaded
    if (window.PraxisCashier) {
      console.log('✅ Praxis SDK already loaded');
      resolve(window.PraxisCashier);
      return;
    }

    // Check if script is already being loaded
    const existingScript = document.querySelector(`script[src="${PRAXIS_CONFIG.sdkUrl}"]`);
    if (existingScript) {
      console.log('⏳ Praxis SDK script already exists, waiting for load...');
      existingScript.addEventListener('load', () => {
        console.log('✅ Existing Praxis SDK script loaded');
        resolve(window.PraxisCashier);
      });
      existingScript.addEventListener('error', (error) => {
        console.error('❌ Existing Praxis SDK script failed to load:', error);
        reject(error);
      });
      return;
    }

    // Create and load script
    const script = document.createElement('script');
    script.src = PRAXIS_CONFIG.sdkUrl;
    script.async = true;

    script.onload = () => {
      console.log('📦 Praxis SDK script loaded, checking for PraxisCashier...');
      if (window.PraxisCashier) {
        console.log('✅ PraxisCashier found on window object');
        resolve(window.PraxisCashier);
      } else {
        console.error('❌ PraxisCashier not found on window object after script load');
        console.log('🔍 Available window properties:', Object.keys(window).filter(key => key.toLowerCase().includes('praxis')));
        reject(new Error('PraxisCashier not found after script load'));
      }
    };

    script.onerror = (error) => {
      console.error('❌ Failed to load Praxis SDK script:', error);
      console.error('🌐 SDK URL:', PRAXIS_CONFIG.sdkUrl);
      reject(new Error('Failed to load Praxis SDK'));
    };

    console.log('📥 Adding Praxis SDK script to document head...');
    document.head.appendChild(script);
  });
};

// Initialize Praxis payment
export const initializePraxisPayment = async (config) => {
  try {
    // Validate configuration
    const validation = validatePraxisConfig();
    if (!validation.isValid) {
      throw new Error(`Praxis configuration error: ${validation.errors.join(', ')}`);
    }

    // Load SDK
    const PraxisCashier = await loadPraxisSDK();
    
    // Create cashier instance
    const cashier = new PraxisCashier(config);
    
    return cashier;
  } catch (error) {
    console.error('Error initializing Praxis payment:', error);
    throw error;
  }
};
