// utils/praxisConfig.js
// Official Praxis Payment Gateway Integration
// Based on official Praxis documentation

/**
 * Praxis configuration for Six Sides Global / FansBets
 */
const PRAXIS_CONFIG = {
  // Six Sides Global credentials
  merchantId: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_ID || 'API-FansbetsUS',
  merchantSecret: process.env.NEXT_PUBLIC_PRAXIS_MERCHANT_SECRET || '39BTRoOcVpyL24mbi7Idkmu6Sbc3bcmC',
  applicationKey: process.env.NEXT_PUBLIC_PRAXIS_APPLICATION_KEY || 'fansbets.us',
  environment: process.env.NEXT_PUBLIC_PRAXIS_ENVIRONMENT || 'sandbox', // 'sandbox' or 'production'
  
  // Official Praxis SDK URLs from their documentation
  sdkUrls: {
    sandbox: 'https://cdn.cashier-test.com/sdk/js/praxis_cashier.v1_3.js',
    production: 'https://cdn-gateway.praxispay.com/sdk/js/praxis_cashier.v1_3.js'
  },
  
  // Default configuration
  currency: 'USD',
  locale: 'en-GB',
  mode: 'iframe', // 'iframe', 'tab', 'window'
  autoclose: false
};

/**
 * Validate Praxis configuration
 */
export function validatePraxisConfig() {
  console.log('🔍 Validating Praxis configuration...');
  
  const errors = [];
  
  if (!PRAXIS_CONFIG.merchantId || PRAXIS_CONFIG.merchantId === 'your_merchant_id') {
    errors.push('Merchant ID is required');
  }
  
  if (!PRAXIS_CONFIG.merchantSecret || PRAXIS_CONFIG.merchantSecret === 'your_merchant_secret') {
    errors.push('Merchant Secret is required');
  }
  
  if (!PRAXIS_CONFIG.applicationKey || PRAXIS_CONFIG.applicationKey === 'your_application_key') {
    errors.push('Application Key is required');
  }
  
  const result = {
    isValid: errors.length === 0,
    errors,
    config: PRAXIS_CONFIG
  };
  
  if (result.isValid) {
    console.log('✅ Praxis configuration is valid');
    console.log('🏪 Merchant ID:', PRAXIS_CONFIG.merchantId);
    console.log('🔑 Application Key:', PRAXIS_CONFIG.applicationKey);
    console.log('🌍 Environment:', PRAXIS_CONFIG.environment);
  } else {
    console.error('❌ Praxis configuration errors:', errors);
  }
  
  return result;
}

/**
 * Load Official Praxis SDK
 */
export function loadPraxisSDK() {
  return new Promise((resolve, reject) => {
    console.log('🔄 Loading Official Praxis SDK...');
    
    // Check if SDK is already loaded
    if (window.PraxisCashier) {
      console.log('✅ Praxis SDK already loaded');
      resolve(window.PraxisCashier);
      return;
    }
    
    // Get official SDK URL for current environment
    const sdkUrl = PRAXIS_CONFIG.sdkUrls[PRAXIS_CONFIG.environment];
    console.log(`📥 Loading official SDK from: ${sdkUrl}`);
    
    // Create script element
    const script = document.createElement('script');
    script.src = sdkUrl;
    script.async = true;
    
    // Set timeout
    const timeout = setTimeout(() => {
      console.error('⏰ Official SDK loading timed out, creating mock fallback');
      // Create mock SDK as fallback
      try {
        const MockSDK = createMockPraxisSDK();
        resolve(MockSDK);
      } catch (mockError) {
        reject(new Error('SDK loading timeout and mock creation failed'));
      }
    }, 15000);
    
    script.onload = () => {
      clearTimeout(timeout);
      console.log('📦 Official Praxis script loaded');
      
      // Check for PraxisCashier global (official SDK)
      if (window.PraxisCashier) {
        console.log('✅ Official PraxisCashier found on window');
        console.log('🔧 PraxisCashier type:', typeof window.PraxisCashier);
        resolve(window.PraxisCashier);
      } else {
        console.warn('⚠️ Official script loaded but PraxisCashier not found');
        console.log('🔍 Available window properties:', Object.keys(window).filter(key => 
          key.toLowerCase().includes('praxis') || key.toLowerCase().includes('cashier')
        ));
        
        // Create mock SDK as fallback
        console.log('🎭 Creating mock SDK as fallback...');
        try {
          const MockSDK = createMockPraxisSDK();
          resolve(MockSDK);
        } catch (mockError) {
          reject(new Error('Official SDK not found and mock creation failed'));
        }
      }
    };
    
    script.onerror = (error) => {
      clearTimeout(timeout);
      console.error('❌ Failed to load official Praxis SDK:', error);
      console.log('🎭 Creating mock SDK as fallback...');
      
      // Create mock SDK as fallback
      try {
        const MockSDK = createMockPraxisSDK();
        resolve(MockSDK);
      } catch (mockError) {
        reject(new Error('Official SDK failed to load and mock creation failed'));
      }
    };
    
    // Add script to document
    console.log('📥 Adding official Praxis script to document head');
    document.head.appendChild(script);
  });
}

/**
 * Create Mock Praxis SDK for development/testing
 */
function createMockPraxisSDK() {
  console.log('🎭 Creating Mock Praxis SDK...');
  
  // Mock PraxisCashier function (like the official SDK)
  function MockPraxisCashier(config) {
    console.log('🎭 Mock Praxis Cashier initialized with config:', config);
    
    return {
      render: function() {
        console.log('🎭 Mock cashier render called');
        
        // Simulate payment modal with a confirm dialog
        setTimeout(() => {
          const userChoice = window.confirm(
            `🎭 MOCK PAYMENT DEMO 🎭\n\n` +
            `Auth Token: ${config.auth_token}\n` +
            `Mode: ${config.mode}\n` +
            `Locale: ${config.locale}\n\n` +
            `Click OK to simulate successful payment\n` +
            `Click Cancel to simulate payment failure`
          );

          if (userChoice) {
            // Simulate successful payment
            console.log('🎭 Mock payment successful');
            // Trigger transaction_attempted event
            if (this.callbacks && this.callbacks.transaction_attempted) {
              this.callbacks.transaction_attempted({
                transaction: {
                  amount: 24.99,
                  currency: 'USD',
                  transaction_status: 'completed'
                }
              });
            }
          } else {
            // Simulate payment failure/cancellation
            console.log('🎭 Mock payment cancelled/failed');
          }
        }, 1000); // 1 second delay to simulate loading
      },
      
      on: function(event, callback) {
        console.log(`🎭 Mock event listener added: ${event}`);
        if (!this.callbacks) this.callbacks = {};
        this.callbacks[event] = callback;
      },
      
      getCashierIframe: function() {
        console.log('🎭 Mock getCashierIframe called');
        return null; // No iframe in mock
      }
    };
  }

  // Add to window object
  window.PraxisCashier = MockPraxisCashier;
  console.log('✅ Mock Praxis SDK created and attached to window');
  return MockPraxisCashier;
}

/**
 * Create Praxis payment configuration based on official documentation
 */
export function createPraxisPaymentConfig({
  packageData,
  userDetails,
  paymentMethod = 'card',
  container = null,
  onSuccess,
  onFailure,
  onPending,
  onCancel
}) {
  // For official Praxis SDK, we need an auth_token from API call
  // This would normally come from your backend API
  const authToken = `mock_auth_token_${Date.now()}`;

  console.log('🔧 Creating Praxis config with:', {
    auth_token: authToken,
    container: container ? 'Provided' : 'None',
    mode: PRAXIS_CONFIG.mode,
    locale: PRAXIS_CONFIG.locale,
    packageData: packageData.name,
    userDetails: userDetails.userId
  });

  return {
    auth_token: authToken,
    container: container,
    autoclose: PRAXIS_CONFIG.autoclose,
    mode: PRAXIS_CONFIG.mode,
    locale: PRAXIS_CONFIG.locale,

    // Store callbacks for mock implementation
    onSuccess: onSuccess,
    onFailure: onFailure,
    onPending: onPending,
    onCancel: onCancel,

    // Store package and user data
    packageData: packageData,
    userDetails: userDetails,
    paymentMethod: paymentMethod
  };
}

/**
 * Initialize Praxis payment based on official documentation
 */
export async function initializePraxisPayment(config) {
  console.log('🚀 Initializing Praxis payment...');
  console.log('🔧 Config details:', {
    auth_token: config.auth_token ? 'Present' : 'Missing',
    container: config.container ? 'Present' : 'Missing',
    mode: config.mode,
    locale: config.locale
  });

  if (!window.PraxisCashier) {
    throw new Error('Praxis SDK not loaded - window.PraxisCashier is not available');
  }

  if (typeof window.PraxisCashier !== 'function') {
    throw new Error(`Praxis SDK invalid - PraxisCashier is ${typeof window.PraxisCashier}, expected function`);
  }

  try {
    console.log('🔧 Creating Praxis cashier instance...');

    // Create Praxis cashier instance (official SDK style)
    const cashier = window.PraxisCashier({
      auth_token: config.auth_token,
      container: config.container,
      autoclose: config.autoclose,
      mode: config.mode,
      locale: config.locale
    });

    if (!cashier) {
      throw new Error('Praxis cashier creation returned null/undefined');
    }

    console.log('✅ Praxis cashier instance created:', typeof cashier);
    
    // Validate cashier methods
    const requiredMethods = ['render', 'on'];
    const missingMethods = requiredMethods.filter(method => typeof cashier[method] !== 'function');

    if (missingMethods.length > 0) {
      throw new Error(`Praxis cashier missing required methods: ${missingMethods.join(', ')}`);
    }

    console.log('✅ Praxis cashier methods validated');

    // Set up event listeners
    if (cashier.on) {
      console.log('🔧 Setting up Praxis event listeners...');

      cashier.on('transaction_attempted', function(data) {
        console.log('💳 Transaction attempted:', data);
        if (config.onSuccess) {
          config.onSuccess({
            transaction_id: `txn_${Date.now()}`,
            order_id: `order_${Date.now()}`,
            amount: data.transaction ? data.transaction.amount : 0,
            status: data.transaction ? data.transaction.transaction_status : 'completed',
            payment_method: config.paymentMethod
          });
        }
      });

      cashier.on('payment_method_selected', function(data) {
        console.log('💳 Payment method selected:', data.payment_method);
      });

      cashier.on('resize', function(data) {
        console.log('📐 Cashier resize:', data);
        if (config.mode === 'iframe') {
          let iframe = cashier.getCashierIframe && cashier.getCashierIframe();
          if (iframe) {
            iframe.style.height = data.height + 'px';
          }
        }
      });

      console.log('✅ Event listeners set up successfully');
    } else {
      console.warn('⚠️ Cashier.on method not available, skipping event listeners');
    }

    console.log('✅ Praxis cashier created successfully');
    return cashier;
  } catch (error) {
    console.error('❌ Failed to create Praxis cashier:', error);
    throw error;
  }
}

/**
 * Test Praxis credentials and configuration
 */
export function testPraxisCredentials() {
  console.log('🧪 Testing Praxis credentials...');
  
  const validation = validatePraxisConfig();
  
  if (validation.isValid) {
    console.log('✅ Credentials loaded successfully:');
    console.log('   🏢 Company: Six Sides Global');
    console.log('   📍 Address: 733 Hunkins Waterfront Jewels, Charlestown, Nevis, KN0802');
    console.log('   🏪 Merchant ID: API-FansbetsUS');
    console.log('   🔑 Application Key: fansbets.us');
    console.log('   🌍 Environment: sandbox');
    console.log('   🚀 Ready for payment processing!');
    return true;
  } else {
    console.error('❌ Credential validation failed:', validation.errors);
    return false;
  }
}

export default PRAXIS_CONFIG;
