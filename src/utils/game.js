'use client';
import ZeroBalancePopUp from '@/components/Models/ZeroBalancePopUp';
import useAuthStore from '@/store/useAuthStore';
import { useRouter } from 'next/navigation';
import { slugify } from './helper';
import Auth from '@/components/Auth';
import useAuthTab from '@/store/useAuthTab';

export const useGameUtils = (gameName, provider) => {
    const { isAuthenticated, userWallet, coin, userDetails } = useAuthStore(
        (state) => state,
    );
    const { setSelectedTab } = useAuthTab((state) => state);
    
    const router = useRouter();
    function handleClickGame(gameName, provider) {
      console.log("🚀 ~ handleGameClick ~ gameName, provider:", gameName, provider)
    if (userDetails?.isRestrict) {
      toast.error('You are restricted, Please contact administrator');
      return;
    }
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 0);
      setSelectedTab(0);
      openModal(<Auth />);
    } else {
      if (coin === 'GC' && userWallet.gcCoin === 0) {
        openModal(
          <ZeroBalancePopUp message="You have zero GC Coins! Please add funds." />,
        );
        return;
      } else if (coin === 'SC' && userWallet.scCoin === 0) {
        openModal(
          <ZeroBalancePopUp message="You have zero SC Coins! Please add funds." />,
        );
        return;
      } else {
        router.push(`/game/${slugify(provider)}/${slugify(gameName)}`);
      }
    }
  }
  return {handleClickGame};
};
