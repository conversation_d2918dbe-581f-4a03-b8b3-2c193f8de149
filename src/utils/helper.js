import Cookies from 'js-cookie';
export const fixedTo4DecimalPlaces = (floatStr) => {
  const floatValue = parseFloat(floatStr);
  if (Number.isNaN(floatValue)) {
    return '0';
  }

  const [integerPart, decimalPart = ''] = floatValue.toString().split('.');

  const truncatedDecimalPart = decimalPart.slice(0, 4).padEnd(4, '0');

  return `${integerPart}.${truncatedDecimalPart}`;
};

export const validateTextInput = (value) => {
  const regex = /^[a-zA-Z0-9]+$/; // Allows letters (A-Z, a-z) and numbers (0-9)
  return value === '' || regex.test(value);
};

export const validateNameInput = (value) => {
  const regex = /^[A-Za-z0-9 ]*$/; // Allow letters, numbers, spaces
  return value === '' || regex.test(value);
};

export const validateNumberInput = (value) => {
  const regex = /^\d{0,10}$/;
  return value === '' || regex.test(value);
};

export const validateSpace = (value) => {
  const regex = /\s/;
  return regex.test(value);
};

export const validatePassword = (value) => {
  const regex = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{6,12}$/;
  return regex.test(value);
};

export const validateZipCodeInput = (value) => {
  const regex = /^[0-9]{0,9}$/;
  return value === '' || regex.test(value);
};

export const validateAlphabetInput = (value) => {
  const regex = /^[A-Za-z]*$/; // Only allows letters (no spaces, numbers, or special characters)
  return regex.test(value);
};

export const eighteenYearsAgo = () => {
  const today = new Date();
  return new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
};

export function formatDateTime(timestamp) {
  const date = new Date(timestamp);
  const today = new Date();

  // Check if the date is today
  const isToday =
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear();

  // Check if the date is yesterday
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);
  const isYesterday =
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear();

  const timeString = date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  });

  if (isToday) {
    return `Today, ${timeString}`;
  }
  if (isYesterday) {
    return `Yesterday, ${timeString}`;
  }
  const dateString = date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
  return `${dateString}, ${timeString}`;
}

export const isValidURL = (str) => {
  const pattern = new RegExp(
    '^(https?:\\/\\/)?' + // protocol
      '((([a-zA-Z\\d]([a-zA-Z\\d-]*[a-zA-Z\\d])*)\\.)+[a-zA-Z]{2,}|' + // domain name and extension
      '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
      '(\\:\\d+)?' + // port
      '(\\/[-a-zA-Z\\d%_.~+]*)*' + // path
      '(\\?[;&a-zA-Z\\d%_.~+=-]*)?' + // query string
      '(\\#[-a-zA-Z\\d_]*)?$',
    'i', // fragment locator
  );
  return !!pattern.test(str);
};

export const calculateRemainingTime = (claimedAt) => {
  const now = new Date().getTime();
  const targetDate = claimedAt;

  const targetTime = new Date(targetDate);
  // targetTime.setDate(targetTime.getDate() + 1);

  const timeDifference = targetTime - now;

  if (timeDifference <= 0) return { hours: '00', minutes: '00', seconds: '00' };
  let seconds = Math.floor((timeDifference / 1000) % 60);
  let minutes = Math.floor((timeDifference / 1000 / 60) % 60);
  let totalHours = Math.floor(timeDifference / (1000 * 3600));

  if (totalHours < 10) {
    totalHours = `0${totalHours}`;
  }
  if (minutes < 10) {
    minutes = `0${minutes}`;
  }
  if (seconds < 10) {
    seconds = `0${seconds}`;
  }

  return { hours: totalHours, minutes, seconds };
};

export const formatValueWithK = (amount) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0';
  }

  let finalAmount;
  if (amount >= 1000000) {
    finalAmount = amount / 1000000;
    return finalAmount % 1 !== 0
      ? `${finalAmount.toFixed(2)}M`
      : `${finalAmount}M`;
  }

  if (amount < 1000) {
    finalAmount = amount;
    return finalAmount % 1 !== 0 ? finalAmount.toFixed(2) : finalAmount;
  }

  finalAmount = amount / 1000;

  return finalAmount % 1 !== 0
    ? `${finalAmount.toFixed(2)}K`
    : `${finalAmount}K`;
};

export const getAllSCValues = (wheelConfiguration) => {
  if (!wheelConfiguration || wheelConfiguration.length === 0) {
    return [];
  }
  return wheelConfiguration.map((wheel) => formatValueWithK(wheel.sc));
};

export const getAllGCValues = (wheelConfiguration) => {
  if (!wheelConfiguration || wheelConfiguration.length === 0) {
    return [];
  }
  return wheelConfiguration.map((wheel) => formatValueWithK(wheel.gc));
};

export const getAccessToken = () => Cookies.get('accessToken');

export const setAccessToken = (token) => {

  Cookies.set('accessToken', token, {
    expires: 1, // Expires in 1 day
    secure: true, // Ensure cookies are sent over HTTPS
    sameSite: 'Strict', // Prevent cross-site request forgery
  });
};

export const removeAccessToken = () => {
  Cookies.remove('accessToken');
};

export const slugify = (title) => {
  return title.replace(/ /g, '-').toLowerCase();
};

export const unSlugify = (slug) => {
  return slug.replace(/-/g, ' ').toLowerCase();
};

export const formatDateWithSuffix = (dateStr) => {
  const [day, month, year] = dateStr.split('-').map(Number);
  const date = new Date(year, month - 1, day);

  const dayNum = date.getDate();
  const monthName = date.toLocaleString('default', { month: 'long' });

  const getDaySuffix = (n) => {
    if (n >= 11 && n <= 13) return 'th';
    switch (n % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  };

  return `${monthName} ${dayNum}${getDaySuffix(dayNum)}`;
};

export const userStatusColor = (status) => {
  switch (status) {
    case 'AVAILABLE':
      return 'bg-green-400 '; // Green with subtle glow
    case 'RECENTLY_ACTIVE':
      return 'bg-[#ffc107] '; // Yellow with subtle glow
    case 'BUSY':
      return 'bg-[#ffa500] '; // Yellow with subtle glow
    case 'AWAY_MODE':
      return 'bg-[#f8f9fa] border border-[#ced4da] '; // Light gray with a slight shadow
    case 'GHOST_MODE':
      return 'bg-[#ff0000] '; // Dark gray with a subtle glow
    default:
      return 'bg-gray-500'; // Default color in case of unknown status
  }
};
export const failedImageCache = new Set();
