'use client';

import axios from 'axios';
import Cookies from 'js-cookie';
import { getAccessToken, setAccessToken } from './helper';


const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  withCredentials: true,
});

axiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = getAccessToken(); // Retrieve token from cookies
    if (accessToken) {
      config.headers['accesstoken'] = accessToken; // Add the token to the custom header
    }
    return config;
  },

  (error) => Promise.reject(error),
);

axiosInstance.interceptors.response.use(
  (res) => {
    // Save accessToken to cookies if available
    const accessToken = res.headers.get("accessToken") || res.headers.get("Accesstoken");
    console.log(accessToken, "::::::::accessToken")
    if (accessToken) {
      setAccessToken(accessToken)
    }
    return res.data; // Return response data
  },
  (error) => {
    const status = error.response?.status;
    const errorCode = error?.response?.data?.errors?.[0]?.errorCode;

    if (status === 403) {
      window.dispatchEvent(new Event('logout'));
    }

    return Promise.reject(error);
  },
);

const makeRequest = async (
  url,
  method,
  data = {},
  params = {},
  headers = { 'Content-Type': 'application/json' },
) => {
  return axiosInstance({ url, method, data, params, headers });
};

const requestHelper = (method) => (url, data, params, headers) =>
  makeRequest(url, method, data, params, headers);

export const getRequest = requestHelper('GET');
export const postRequest = requestHelper('POST');
export const putRequest = requestHelper('PUT');
export const deleteRequest = requestHelper('DELETE');
