import { getRequest, postRequest, putRequest } from './axios';

// GET REQUESTS
export const getUserProfile = (params) => getRequest('/user/profile', params);
export const getBanners = () => {
  return getRequest('/details/banners');
};
export const getState = (params) => getRequest('/details/state', {}, params);
export const getDeliveryCities = (params) =>
  getRequest('/inventory/city', {}, params);
export const getAllBets = (params) => getRequest('user/bets', {}, params);
export const getFriends = (params) => getRequest('/friend/my', {}, params);
export const getAllCmsPages = (param) => getRequest('/details/cms', {}, param);
export const getIgnoredUsers = (params) =>
  getRequest('/user/ignored', {}, params);
export const getPublicChats = ({ pageParam = 1, limit = 20 }) =>
  getRequest(`/user/chat?page=${pageParam}&limit=${limit}`);
export const getGroupChats = ({ pageParam = 1, limit = 20, groupId }) =>
  getRequest(`/user/chat?groupId=${groupId}&page=${pageParam}&limit=${limit}`);
export const getGroupDetails = (gId) =>
  getRequest(`user/chat-group?groupId=${gId}`);
export const getGroupList = (params) =>
  getRequest('/user/chat-group', {}, params);
export const getGroupJoinRequestList = (params) =>
  getRequest('/user/chat-group/invitation', {}, params);
export const getPublicGroupList = (params) =>
  getRequest('/user/chat-group/other', {}, params);
export const getPublicGroupDetails = (gId) =>
  getRequest(`/user/chat-group/other?groupId=${gId}`);
export const getGroupDetail = (params) => {
  return getRequest(`/user/chat-group?groupName=${params}`);
};
export const getPrivateChat = (params) => {
  return getRequest('/user/private-chat', {}, params);
};
export const getUserTags = (params) =>
  getRequest('/user/tag-users', {}, params);
export const getRecentChat = (params) =>
  getRequest('/user/get-recent-chat', {}, params);
export const getCustomGames = (params) =>
  getRequest('/casino-games/custom-games', params);
export const getCmsPageDetails = (param) =>
  getRequest('/details/cms-detail', {}, param);
export const getFavoritesGames = ({ pageParam = 1, limit = 20 }) => {
  return getRequest(`/casino-games/favorite?page=${pageParam}&limit=${limit}`);
};
export const getSubCategoryGames = ({
  pageParam = 1,
  limit = 18,
  subCategoryId,
}) => {
  return getRequest(
    `/casino-games/?limit=${limit}&page=${pageParam}&subCategoryId=${subCategoryId}`,
  );
};
export const getCountries = (params) =>
  getRequest('/details/countries', params);
export const getPreferences = (params) =>
  getRequest('/user/preferences', params);
export const getFriendsRequest = (params) =>
  getRequest('/friend/requests', params);
export const getLivePlayers = (params) =>
  getRequest('user/live-players', {}, params);
export const getPlayerDetails = (params) =>
  getRequest('user/details', {}, params);
export const getNoticeDetails = (params) =>
  getRequest('/details/notice-detail', {}, params);
export const getNotifications = (params) =>
  getRequest('/user/notification', {}, params);
export const getInventories = ({ pageNo, limit, search }) =>
  getRequest(`/inventory?page=${pageNo}&limit=${limit}&search=${search}`);
export const getCart = (params) => getRequest('/inventory/cart', {}, params);
export const getGetLabels = () => getRequest('/kyc/get-labels');
export const getSpinWheelConfiguration = () =>
  getRequest('/spinWheelConfiguration/getList');
export const getSpinWheelResult = () =>
  getRequest('spinWheelConfiguration/generateIndex');
export const updateWallet = () =>
  getRequest('spinWheelConfiguration/updateWallet');
export const getSpinWheelindex = () =>
  getRequest('spinWheelConfiguration/generateIndex');

export const getAllOrders = (params) =>
  getRequest('/inventory/my-orders', {}, params);

export const getTipsTransactions = () => {
  return getRequest('/user/tip', { limit: 10, page: 1 });
};

export const getVipTierRules = (params) => {
  return getRequest('/rewards/vip-tier', {}, params);
};

export const getRainTransactions = (params) => {
  return getRequest('/user/get-rain-transaction?limit=10&page=1', {}, params);
};
export const getTransactions = (params) => {
  return getRequest('/casino-games/transactions', {}, params);
};

export const getCards = () => {
  return getRequest('/chest-and-card');
};

export const getChestAndCardTransactions = () => {
  return getRequest('/chest-and-card/transactions');
};

export const getChestAndCardHistory = () => {
  return getRequest('/chest-and-card/history');
};

export const getSubCategories = (params) => {
  return getRequest('/casino-games/sub-category', {}, params);
};
export const getFeaturedGames = () => {
  return getRequest('/casino-games/featured-games');
};
export const getUserTasks = () => {
  return getRequest('/user/task');
};
export const getUserClaimedTasks = () => {
  return getRequest('/user/claimed-task');
};
export const getActivePlayerMyFriends = () => {
  return getRequest('/user/public-activity?myFriends=true');
};
export const getActiveGroupMyGroups = () => {
  return getRequest('/user/group-activity?myGroups=true');
};
export const getActivePlayerPublic = () => {
  return getRequest('/user/public-activity');
};
export const getActiveGroupPublic = () => {
  return getRequest('/user/group-activity');
};
export const getConnectedPlay = (params) => {
  return getRequest('/user/connected-play-details', {}, params);
};
export const getBonusFetch = () =>{
  return getRequest('/bonus/fetch-green-bonus-detail')
}
export const claimGreenBonus = () =>{
  return getRequest('/bonus/claim-green-bonus');
}

export const initKYC = () => getRequest('/kyc/init-kyc');
// POST REQUESTS
export const userSignIn = (data) => {
  return postRequest('/user/login', data);
};
// /user/googleLogin
export const userSocialGoogleLogin = (data) =>
  postRequest('/user/googleLogin', data);
export const userSocialFacebookLogin = (data) =>
  postRequest('/user/facebookLogin', data);
export const userSocialDiscordLogin = (data) =>
  postRequest('/user/discord', data);
export const userSocialTwitchLogin = (data) =>
  postRequest('/user/twitch', data);
export const userLogout = () => postRequest('user/logout');
export const userSignUp = (data) => postRequest('/user/sign-up', data);
export const sendPublicChatsMsg = (data) => postRequest('/user/chat', data); // getAgoraToken
export const createGroup = (data) => {
  return postRequest(
    '/user/chat-group',
    data,
    {},
    {
      'Content-Type': 'multipart/form-data',
    },
  );
};

export const getAgoraToken = (data) =>
  postRequest('/agora/generate-token', data);
export const sendDeclinedReq = (data) =>
  postRequest('/agora/decline-call', data);
export const sendTagMsg = (data) => postRequest('/user/tag-users', data);
export const userForgotPassword = (data) =>
  postRequest('user/forgetPassword', data);
export const gameLaunch = (data) =>
  postRequest('/casino-games/launch-game', data);
export const sportsGameLaunch = (data) =>
  postRequest('/casino-games/launch-sports-book-game', data);
export const updateFavorite = (data) =>
  postRequest('/casino-games/favorite', data);
export const twoFactorAuth = (data) =>
  postRequest('/user/generate-otp-2fa', data);
export const twoFactorVerfiyOtp = (data) =>
  postRequest('/user/verify-otp-2fa', data);
export const twoFactorEnable = (data) =>
  postRequest('/user/disable-auth', data);
export const updateEmail = (data) => postRequest('/user/update-email', data);
export const verifyEmail = (data) => postRequest('/user/verifyEmail', data);
export const createFriendRequest = (data) =>
  postRequest('/friend/requests', data);
export const claimFauset = (data) => postRequest('/bonus/faucet-bonus', data);
export const twoFactorVerfiyLoginOtp = (data) =>
  postRequest('/user/verify-otp-2fa-login', data);
export const addToCart = (data) => postRequest('/inventory/cart', data);
export const uploadKycDoc = (data) =>
  postRequest(
    '/kyc/upload-image',
    data,
    {},
    {
      'Content-Type': 'multipart/form-data',
    },
  );

export const joinGroup = (data) =>
  postRequest('/user/chat-group/join', data);
export const sendTip = (data) => postRequest('/user/tip', data);

export const sendRainDrop = (data) => postRequest('/user/rain-drop', data);

export const grabRainDrop = (data) => postRequest('/user/grab-rain-drop', data);
export const claimChest = (data) => postRequest('/chest-and-card/claim', data);
export const claimCardBonus = (data) =>
  postRequest('/chest-and-card/claim-bonus', data);
export const claimVipTier = (data) => {
  return postRequest('/rewards/claim-tier-bonus', data);
};
// PUT REQUESTS
export const updateUserDetails = (data) => putRequest('/user/profile', data,{},{'Content-Type': 'multipart/form-data'});
export const updateGroupBanner = (data) =>
  putRequest('user/chat-group/update-group-banner', data,
    {},
    {
      'Content-Type': 'multipart/form-data',
    },);

export const updateJoinedGroup = (data) =>
  putRequest('/user/chat-group/update-members', data);
export const sendGroupJoinRequest = (data) =>
  postRequest('/user/chat-group/invitation', data);
export const userChangePassword = (data) =>
  putRequest('/user/changePassword', data);
export const uploadProfileImage = (data) => {
  return putRequest(
    '/user/self-profile',
    data,
    {},
    {
      'Content-Type': 'multipart/form-data',
    },
  );
};
export const userAction = (data) => putRequest('/user/action', data);

export const updatePreferenceDetails = (data) =>
  putRequest('user/preferences', data);

export const updateGroup = (data) => putRequest('/user/chat-group', data);

export const updateFriendRequest = (data) =>
  putRequest('/friend/requests', data);

export const unFriendRequest = (data) => putRequest('/friend/unfriend', data);

export const updateCart = (data) => putRequest('/inventory/cart', data);
export const updateDeliveryAddress = (data) =>
  putRequest('/inventory/update-cart-address');
export const confirmOrder = (data) =>
  putRequest('/inventory/confirm-order', data);
export const updateCartAddress = (data) =>
  putRequest('/inventory/update-cart-address', data);
export const cancelOrder = (data) =>
  putRequest('/inventory/cancel-order', data);

export const claimUserTask = (data) => {
  return putRequest('/user/task', data);
};
export const updateUserStatus = (data) =>
  putRequest('/user/update-status', data);
export const updateNotification = (data) =>
  putRequest('/user/notification', data);
export const acceptDeclineGroupRequest = (data) =>
  putRequest('/user/chat-group/accept-decline', data);
// DELETE REQUESTS
