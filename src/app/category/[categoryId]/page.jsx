'use client';

import React, { useCallback, useEffect, useRef } from 'react';
import GameCard from '@/components/Common/GameCard';
import MainLoader from '@/components/Common/Loader/MainLoader';
import { getSubCategoryGames } from '@/utils/apiCalls';
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import defaultImage from '../../../assets/images/png/default-image.png';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
import ZeroBalancePopUp from '@/components/Models/ZeroBalancePopUp';
import useCategoryTitleStore from '@/store/useCategoryTitleStore';
import { slugify } from '@/utils/helper';
function GamePage({ params }) {
  console.log(params, '::::::params');
  const queryClient = useQueryClient(); // Access the query client
  const searchParams = useSearchParams();
  const router = useRouter();
  const search = searchParams.get('id');
  const { isAuthenticated, userWallet, coin } = useAuthStore((state) => state);
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useModalStore((state) => state);
  const { categoryTitle } = useCategoryTitleStore((state) => state);
  useEffect(() => {
    // Reset the query data when the component mounts
    queryClient.resetQueries(['GET_SUBCATEGORY_GAMES'], { exact: true });
  }, [queryClient]);

  const observerRef = useRef(null)

  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    status,
  } = useInfiniteQuery({
    queryKey: ['GET_SUBCATEGORY_GAMES'],
    queryFn: ({ pageParam = 1 }) => {
      console.log(pageParam, 'Fetching Page');
      return getSubCategoryGames({
        pageParam,
        limit: 20,
        subCategoryId: search,
      });
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const { rows = [], count = 0 } = lastPage?.data?.data || {};
      const totalFetched = allPages.flatMap(
        (page) => page?.data?.data?.rows,
      ).length;

      console.log(totalFetched, 'Total Fetched');

      if (totalFetched >= count) {
        return undefined; // No more pages to fetch
      }

      const currentPage = allPages.length; // Number of fetched pages
      console.log(currentPage, 'Next Page');
      return currentPage + 1;
    },
  });

  const allGames = data?.pages?.flatMap((page) => page?.data?.data?.rows) || [];
  const loadMoreRef = useCallback(
    (node) => {
      if (isFetchingNextPage) return;
      if (observerRef.current) observerRef.current.disconnect()

      observerRef.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasNextPage) {
          fetchNextPage();
        }
      });

      if (node) observerRef.current.observe(node)
    },
    [isFetchingNextPage, fetchNextPage, hasNextPage]
  )
  const handleGameClick = (gameName, provider) => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 0);
      setSelectedTab(0);
      openModal(<Auth />);
      return;
    }
    if ((coin === "GC" && userWallet.gcCoin === 0) || (coin === "SC" && userWallet.scCoin === 0)) {
      openModal(<ZeroBalancePopUp message={`You have zero ${coin} Coins! Please add funds.`} />);
      return;
    }
    router.push(`/game/${slugify(provider)}/${slugify(gameName)}`);
  };

  return status === 'pending' ? (
    <section className="item-center mb-10 flex justify-center rounded-lg p-4 shadow-container">
      <div className="text-white text-center">
        <MainLoader className="w-32" />
      </div>
    </section>
  ) : status === 'error' ? (
    <p>Error: {error.message}</p>
  ) : (
    <section className={`mb-10 rounded-lg  shadow-container  ${isAuthenticated ? 'md:mt-10' : ''}`}>
      <div className="mb-6 flex items-center justify-between gap-4">
        <h6 className="text-white text-xl font-bold capitalize">
          {categoryTitle} Games
        </h6>
      </div>
      {allGames.length === 0 ? (
        <div className="text-white text-center">No games found</div>
      ) : (
        <div className="grid grid-cols-4 gap-x-5 gap-y-8 md:grid-cols-4 lg:grid-cols-5  max-sm:grid-cols-3  max-sm:gap-[10px]">
          {allGames.map((game, index) => (
            <GameCard
              key={`${game?.name}-${index}`}
              src={
                game?.MasterCasinoGame?.moreDetails?.url_thumb ||
                game?.url_thumb ||
                defaultImage
              }
              alt={game?.name}
              gameId={game?.masterCasinoGameId}
              isFavorite={game?.FavoriteGames}
              width="200"
              height="200"
              onClick={() => handleGameClick(game?.name, game?.MasterCasinoGame?.moreDetails?.product)}
            />
          ))}
        </div>
      )}

      {hasNextPage && (
        <div ref={loadMoreRef} className="mt-6 h-10 w-full text-center">
          {isFetchingNextPage && (
            <MainLoader className="mx-auto w-10 text-white" />
          )}
        </div>
      )}
    </section>
  );
}

export default GamePage;
