'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import PrimaryButtonOutline from '@/components/Common/Button/PrimaryButtonOutline';
import useIgnoredUsers from '@/hooks/useIgnoredUsers';
import useUserActions from '@/hooks/useUserActions';
import ChatAvatar from '@/components/ChatWindow/ChatAvatar';

function IgnoredUsersPage() {
  // const [selectedOption, setSelectedOption] = useState(null);
  const [imageErrors, setImageErrors] = useState({});
  const { ignoredUsers, ignoredUsersLoading, refetch } = useIgnoredUsers();
  const { unignoreUser } = useUserActions({
    refetch,
  });


  const handleUnIgnore = (selectedUserId) => {
    unignoreUser(selectedUserId);
  };

  return (
    <div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-white-100">
              <th className="whitespace-nowrap px-2 py-4 text-left text-base font-bold text-steelTeal-1000 max-sm:text-sm" />
              {/* <th className="whitespace-nowrap px-2 py-4 text-left text-base font-bold capitalize text-steelTeal-1000 max-sm:text-sm">
                nick name
              </th> */}
              <th className="whitespace-nowrap px-2 py-4 text-left text-base font-bold capitalize text-steelTeal-1000 max-sm:text-sm">
                user name
              </th>
              <th className="whitespace-nowrap px-2 py-4 text-left text-base font-bold capitalize text-steelTeal-1000 max-sm:text-sm">
                action
              </th>
            </tr>
          </thead>
          <tbody>
            {ignoredUsers?.rows?.length > 0 ? (
              ignoredUsers.rows.map((ignoredUser) => (
                <tr key={ignoredUser?.userId}>
                  <td className="whitespace-nowrap text-left text-sm font-normal text-white-1000">
                    {ignoredUser?.relationUser?.profileImage &&
                     !imageErrors[ignoredUser?.relationUser?.userId] ? (
                      <Image
                        src={ignoredUser?.relationUser?.profileImage}
                        width={50}
                        height={50}
                        className="h-[50px] w-[50px] rounded-full object-cover"
                        alt="Profile"
                        onError={() => {
                          setImageErrors(prev => ({
                            ...prev,
                            [ignoredUser?.relationUser?.userId]: true
                          }));
                        }}
                      />
                    ) : (
                      <ChatAvatar
                        profileImage={null}
                        firstName={ignoredUser?.relationUser?.firstName}
                        lastName={ignoredUser?.relationUser?.lastName}
                        imageClassName="h-[50px] w-[50px] rounded-full object-cover"
                        imageWidth={50}
                        imageHeight={50}
                        avatarSize={50}
                      />
                    )}
                  </td>
                  <td className="whitespace-nowrap px-2 py-4 text-left text-base font-normal text-white-1000 max-sm:text-sm">
                    {ignoredUser?.relationUser?.username}
                  </td>
                  <td className="whitespace-nowrap px-2 py-4 text-left text-base font-normal text-scarlet-700 max-sm:text-sm">
                    <PrimaryButtonOutline
                      onClick={() =>
                        handleUnIgnore(ignoredUser?.relationUser?.userId)
                      }
                    >
                      <div className="text-white-1000">Unignore</div>
                    </PrimaryButtonOutline>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={3}
                  className="px-4 py-6 text-center text-base text-white-1000"
                >
                  No ignored users found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default IgnoredUsersPage;
