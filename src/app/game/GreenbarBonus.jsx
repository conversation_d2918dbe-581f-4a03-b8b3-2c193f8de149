'use client';

import {
  useGreenBonusClaim
} from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import useGameStore from '@/store/useGameStore';
import useGreenBonusStore from '@/store/useGreenBonusStore';
import { getAccessToken } from '@/utils/helper';
import { walletSocket } from '@/utils/socket';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';

function GreenbarBonus() {
  const { gameId } = useParams();
  const { isAuthenticated, coin } = useAuthStore((state) => state);
  const accessToken = getAccessToken();
  const [animatedWidth, setAnimatedWidth] = useState('0%');
  const [showBoost, setShowBoost] = useState(false);
  const { gameDetails } = useGameStore();
  const [showAnimation, setAnimation] = useState(false)
  const {
    isGreenBonusApplicable,
    betAmountToClaimBonus,
    totalBetAmountTill,
    setGreenBonusData,
    setTotalBetAmountTill,
    newSession
  } = useGreenBonusStore((state) => ({
    isGreenBonusApplicable: state.isGreenBonusApplicable,
    betAmountToClaimBonus: state.betAmountToClaimBonus,
    totalBetAmountTill: state.totalBetAmountTill,
    setGreenBonusData: state.setGreenBonusData,
    setTotalBetAmountTill: state.setTotalBetAmountTill,
    newSession: state.newSession
  }));

  // State to control confetti
  const [prevProgress, setPrevProgress] = useState(0);
  const [justReset, setJustReset] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  // const cliamGreenBonusMutation = useGreenBonusClaim({
  //   onSuccess: (response) => { },
  //   onError: (error) => {
  //     const message =
  //       error.response?.data?.errors?.[0]?.description ||
  //       'Failed to claim green bonus';
  //     // setError(message);
  //     toast.error(message);
  //   },
  // });
  const isClaimGreenBonusCalled = (data) => {
    const { betAmountToClaimBonus, totalBetAmountTill } =
      useGreenBonusStore.getState();
    console.log(
      'CLAIM_GREEN_BONUS triggered',
      betAmountToClaimBonus,
      totalBetAmountTill,
    );
    setTotalBetAmountTill({
      totalBetAmountTill: betAmountToClaimBonus,
    });
    const updatedData = {
      ...data,
      data: {
        ...data.data,
        coin
      }
    };
    // cliamGreenBonusMutation.mutate(updatedData);
     // Trigger confetti
    setShowConfetti(true);
    createConfetti();
    setAnimatedWidth(`0%`);
    setAnimation(true);

    // Explicitly set the just reset flag
    setJustReset(true);

    // Show boost after a small delay (after the bar starts filling again)
    setTimeout(() => {
      setShowBoost(true);
      console.log("Setting showBoost to TRUE after claiming bonus");

      // Hide boost after 3 seconds
      setTimeout(() => {
        setShowBoost(false);
        console.log("Setting showBoost to FALSE after timeout");
      }, 4000);
    }, 3000);

    // Stop confetti after 5 seconds
    setTimeout(() => {
      setShowConfetti(false);
    }, 5000);
  };
  // Function to create confetti
  const createConfetti = () => {
    const colors = ['#ff0', '#0f0', '#00f', '#f00', '#ff69b4']; // Customize colors
    const confettiCount = 100; // Number of confetti pieces

    for (let i = 0; i < confettiCount; i++) {
      const confetti = document.createElement('div');
      confetti.className = 'confetti';
      confetti.style.position = 'fixed';
      confetti.style.top = '-10px'; // Start above the screen
      confetti.style.left = `${Math.random() * 100}vw`; // Random x-position
      confetti.style.width = `${Math.random() * 10 + 5}px`; // Random width
      confetti.style.height = `${Math.random() * 10 + 5}px`; // Random height
      confetti.style.backgroundColor =
        colors[Math.floor(Math.random() * colors.length)]; // Random color
      confetti.style.opacity = '0.8';
      confetti.style.transform = `rotate(${Math.random() * 360}deg)`; // Random rotation
      confetti.style.zIndex = '1000'; // Ensure it’s on top

      // Animation
      confetti.animate(
        [
          { transform: `translateY(0) rotate(${Math.random() * 360}deg)` },
          {
            transform: `translateY(${window.innerHeight + 10}px) rotate(${Math.random() * 360}deg)`,
          },
        ],
        {
          duration: Math.random() * 2000 + 2000, // Random duration between 2-4 seconds
          easing: 'linear',
          fill: 'forwards',
        },
      );

      document.body.appendChild(confetti);

      // Cleanup: Remove confetti after animation
      setTimeout(() => {
        confetti.remove();
      }, 4000);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      console.log(accessToken, ':::::::::::accessToken');
      walletSocket.auth = { token: accessToken };
      walletSocket.connect();

      const handleClaimGreenBonus = (data) => isClaimGreenBonusCalled(data);

      walletSocket.on('CLAIM_GREEN_BONUS', handleClaimGreenBonus);

      return () => {
        walletSocket.off('CLAIM_GREEN_BONUS');
        walletSocket.disconnect();
      };
    }
  }, [isAuthenticated, accessToken]);

  const progress = betAmountToClaimBonus
    ? (totalBetAmountTill / betAmountToClaimBonus) * 100
    : 0;

  useEffect(() => {
    if (gameDetails?.isGreenBonusApplicable) {
      console.log('gameDetails?.isGreenBonusApplicable this called?????');
      setGreenBonusData(gameDetails);
    }
  }, [gameDetails, setGreenBonusData]);

  useEffect(() => {
    // Calculate progress as before
    let calculatedProgress = 0;
    if (gameDetails?.alreadyClaimedOnce) {
      calculatedProgress = Math.min((progress / 100) * 80 + 20, 100);
    } else if (newSession) {
      calculatedProgress = Math.min((progress / 100) * 80 + 20, 100);
    } else {
      calculatedProgress = Math.min(progress, 100);
    }

    // Check if we've just reset from 100% to near 0

      // For increasing progress, use a longer timeout to delay the start
      const timeout = setTimeout(() => {
        setAnimatedWidth(`${calculatedProgress}%`);
      }, (showAnimation || gameDetails?.alreadyClaimedOnce || newSession) ? 3000 : 300);

      return () => clearTimeout(timeout);
      // Update previous progress for next comparison
      // setAnimation(false);

  }, [progress, gameDetails?.alreadyClaimedOnce, showAnimation, betAmountToClaimBonus, totalBetAmountTill]);

  return (
    <div className="relative w-full">
      {/* Progress Bar */}
      {isGreenBonusApplicable && (
        <div className="mx-auto  w-full max-w-md md:mt-0 xl:mt-4">
          <div className="relative h-[4dvh] lg:h-8 w-full overflow-hidden rounded-full bg-gray-800 shadow-md">
            <div className="absolute inset-0 flex items-center justify-between px-2">
            </div>
            <div
              className={`text-white flex h-full items-center justify-center bg-gradient-to-r from-green-400 to-blue-500 text-xs font-bold
                 ${!showConfetti || showBoost  ?
                  justReset ? 'transition-[width] duration-[6000ms] ease-out' : 'transition-[width] duration-[4000ms] ease-out'
                  : ''
                } origin-left`}
              style={{ width: animatedWidth }}
            >

              <div
                className="bg-white absolute h-4 w-0.5 transition-all duration-[2000ms]"
                style={{
                  // left: gameDetails?.alreadyClaimedOnce? `${Math.min(((progress/100)*80)+20,100)}%`     :` ${Math.min(progress, 100)}%` ,
                  transform: 'translateX(-50%)',
                  left: animatedWidth,
                }}
              ></div>
              {showConfetti && !showBoost ? (
                <span
                className={`absolute ${showBoost ? `left-[60%]`:`left-[50%]`} transform -translate-x-1/2 text-sm font-bold text-[#EFD16F] animate-pulse whitespace-nowrap`}>
                  {/* {progress.toFixed(2)}% */}
                  Bonus Added To Rewards!
                </span>
              ) : (!showBoost &&
                <span className="absolute left-1/2 transform -translate-x-1/2 text-sm md:text-lg font-bold tracking-[4px] xs:tracking-[7px] text-[#EFD16F] whitespace-nowrap">
                  {/* {progress.toFixed(2)}% */}B O N U S
                </span>
              )}
              {showBoost && (
                <span
                  className="absolute left-[3%] text-[13px] md:text-[18px] font-bold text-[#EFD16F]"
                  style={{ zIndex: 20 }} // Make sure it's on top of other elements
                >
                  BOOST!
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default GreenbarBonus;
