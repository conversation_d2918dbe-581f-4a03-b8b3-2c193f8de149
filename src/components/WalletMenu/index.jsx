'use client'
import React from 'react'
import ToggleSwitch from '../ToggleSwitch'
import SCicon from '@/assets/icons/SCicon';
import GCicon from '@/assets/icons/GCicon';
import useAuthStore from '@/store/useAuthStore';
import { formatValueWithK } from '@/utils/helper';
import { usePathname } from 'next/navigation';
import AnimatedButton from '../Common/Button/AnimatedButton';
export const WalletMenu = ({ showWallet }) => {
    const { coin, isAuthenticated, userWallet } = useAuthStore(
        (state) => state,
    );
    const pathname = usePathname();
    const isGamePage = pathname.startsWith('/game/');

    return (
        <>
            <div
                className={`${showWallet ? 'right-0 w-[14.75rem] xl:left-0 flex flex-col gap-[20px]' : 'right-[-100%] w-[14.75rem]'} fixed top-[3.75rem] z-[50] h-[100dvh]  bg-cetaceanBlue-1000 p-4 transition-all duration-500 ease-in-out xl:left-0 xl:top-0 xl:h-dvh`}
            >
                <div className="relative flex flex-row-reverse items-center gap-1 overflow-hidden pb-4">
                    <ToggleSwitch />

                    <div className="flex w-full max-w-[180px] flex-col gap-1">
                        <div
                            className={`relative flex h-8 items-center justify-between gap-2 rounded-3xl bg-primary-500 py-1.5 pl-5 pr-8 font-outfit text-base font-bold ${coin === 'GC' && 'grayscale'}`}
                        >
                            {isAuthenticated &&
                                !isGamePage &&
                                (formatValueWithK(userWallet?.scCoin) || 0)}
                            <SCicon className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2" />
                        </div>

                        <div
                            className={`relative flex h-8 items-center justify-between gap-2 rounded-3xl bg-golden-500 py-1.5 pl-5 pr-8 font-outfit text-base font-bold ${coin === 'SC' && 'grayscale'}`}
                        >
                            {isAuthenticated &&
                                !isGamePage &&
                                (formatValueWithK(userWallet?.gcCoin) || 0)}
                            <GCicon className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2" />
                        </div>
                    </div>
                </div>
                <AnimatedButton />
            </div>
        </>
    )
}
export default WalletMenu