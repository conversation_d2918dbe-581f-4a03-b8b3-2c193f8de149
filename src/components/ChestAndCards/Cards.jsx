import Image from 'next/image';
import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import useCards from '@/hooks/useCards';
import CardHistoryModal from './CardHistoryModal';
import useModalStore from '@/store/useModalStore';
import NoDataFound from '../Common/NoDataFound';
import { useCmsPageDetailsQuery } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import MainLoader from '../Common/Loader/MainLoader';

function Cards() {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { cardsData } = useCards();
  const { closeModal, openModal } = useModalStore((state) => state);

  const [cards, setCards] = useState([]);
  const [activeCard, setActiveCard] = useState(null);

  const {
    data: cmsPageData,
    isLoading,
    error,
  } = useCmsPageDetailsQuery({
    enabled: isAuthenticated,
    pageSlug: 'game-rules-card',
  });

  useEffect(() => {
    if (cardsData?.completeWord && cardsData.completeWord.length > 0) {
      const generatedCards = cardsData?.completeWord?.map((card, index) => ({
        id: card?.cardId,
        src: card?.cardImage,
        alt: `Card ${card?.cardImage}`,
        label: `Card ${card?.cardImage}`,
        number:
          cardsData.collectedLetters.find(
            (collectedLetter) => collectedLetter.cardId == card.cardId,
          )?.quantity || '0',
        isCollected: cardsData?.collectedLetters?.find(
          (collectedLetter) => collectedLetter?.cardId == card.cardId,
        ),
      }));

      setCards(generatedCards);
      setActiveCard(generatedCards[0]);
      console.log('generatedCards', generatedCards);
    }
  }, [cardsData]);

  const handlePrev = () => {
    const currentIndex = cards.findIndex((card) => card.id === activeCard.id);
    const prevIndex = (currentIndex - 1 + cards.length) % cards.length;
    setActiveCard(cards[prevIndex]);
  };

  const handleNext = () => {
    const currentIndex = cards.findIndex((card) => card.id === activeCard.id);
    const nextIndex = (currentIndex + 1) % cards.length;
    setActiveCard(cards[nextIndex]);
  };

  if (!activeCard) {
    return (
      <div className="bg-darkBlue-900 rounded-lg p-4 text-center shadow-lg">
        <NoDataFound className="w-28" />
      </div>
    );
  }

  return (
    <div className="bg-darkBlue-900 rounded-lg p-4 text-center shadow-lg">
      <div className="flex items-center justify-between text-lg font-bold">
        <div className="text-white">
          Completed Words: {cardsData.totalCompleteWords || 0}
        </div>
        <div>
          Bonus Pool:{' '}
          <span className="text-green-900"> {cardsData?.bonusPool} SC</span>
        </div>
      </div>
      <div className="my-4 flex items-center justify-center">
        <div className="flex gap-2">
          {cards.map((card) => (
            <button
              key={card.id}
              className={`relative rounded-lg border-4 p-1 ${activeCard.id === card.id ? 'border-primary-1000' : 'border-transparent'}`}
              onClick={() => setActiveCard(card)}
            >
              {card.number > 0 && (
                <div className="text-white absolute right-1 top-1 rounded-full bg-primary-1000 px-2 py-1 text-sm font-bold">
                  {card.number}
                </div>
              )}
              {
                <Image
                  src={card.src}
                  alt={card.alt}
                  width={100}
                  height={150}
                  className={
                    card.isCollected
                      ? 'rounded-lg shadow-lg '
                      : 'rounded-lg shadow-lg brightness-50 filter'
                  }
                />
              }
            </button>
          ))}
        </div>
      </div>
      <div className="my-4 flex items-center justify-between">
        <button onClick={handlePrev} className="text-white-1000">
          <ChevronLeft className="h-10 w-10" />
        </button>
        <div className="flex gap-2">
          <Image
            src={activeCard.src}
            alt={activeCard.alt}
            width={200}
            height={300}
          />
        </div>
        <button onClick={handleNext} className="text-white-1000">
          <ChevronRight className="h-10 w-10" />
        </button>
      </div>
      <div className="text-md mt-4 flex justify-between  text-white-1000">
        <div className="mb-1">
          <span className="mr-1 font-bold">Duration:</span>
          {new Date(cardsData.startDate).toLocaleDateString()} -{' '}
          {new Date(cardsData.endDate).toLocaleDateString()}
        </div>
        <div>
          <span className="mr-1 font-bold">Status:</span>

          <span className="font-bold text-green-900">
            {' '}
            {cardsData.isActive ? 'Active' : 'Finished'}
          </span>
        </div>
      </div>
      <div className="text-md mt-4 flex justify-center  text-white-1000">
        <button
          onClick={() => openModal(<CardHistoryModal />)}
          className="flex underline"
        >
          View History
        </button>
      </div>
      <div className="text-white border-spacing-1 rounded-lg border border-dashed border-white-1000 p-4 text-left">
        <h4 className="mb-2 font-bold">Game Rule:</h4>
        {isLoading ? (
          <MainLoader className={'w-20'} />
        ) : (
          <div
            className="content-para"
            dangerouslySetInnerHTML={{ __html: cmsPageData?.content?.EN }}
          />
        )}
      </div>
    </div>
  );
}

export default Cards;
