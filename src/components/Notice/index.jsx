'use client';

import React from 'react';
import IconButton from '../Common/Button/IconButton';
import CloseIcon from '@/assets/icons/CloseIcon';
import useNoticeStore from '@/store/useNoticeStore';
import useNotice from '@/hooks/useNotice';
import useModalStore from '@/store/useModalStore';
import NoDataFound from '../Common/NoDataFound';

function Notice() {
  const { closeModal } = useModalStore((state) => state);

  const handleCloseModal = () => {
    closeModal();
  };

  useNotice();
  const { noticeDetails } = useNoticeStore();
  // console.log('noticeDetailss', noticeDetails);

  if (!noticeDetails?.title) {
    return (
      <div
        tabIndex="-1"
        aria-hidden="true"
        className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto "
      >
        <div className="relative w-full max-w-xl p-4">
          <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
            <div className="flex items-center justify-between p-4">
              <div className="flex items-center gap-3">
                <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                  Notice
                </h3>
              </div>

              <IconButton
                onClick={handleCloseModal}
                className="h-6 w-6 min-w-6"
              >
                <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
            <div className="text-white p-10 text-center">
              <NoDataFound className="w-28" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto "
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                {noticeDetails?.title?.EN}
              </h3>
            </div>

            <IconButton onClick={handleCloseModal} className="h-6 w-6 min-w-6">
              <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          <div className="text-white p-4">
            <div
              className="content-para"
              dangerouslySetInnerHTML={{
                __html: noticeDetails?.content?.EN,
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Notice;
