import IconButton from '@/components/Common/Button/IconButton';
import Tooltip from '@/components/Common/Tooltip';
import { rtc } from '@/hooks/useGroupCall';
import useCallStore from '@/store/useCallStore';
import { Mic, MicOff } from 'lucide-react';
import { useEffect, useState } from 'react';


const ToggleMuteButton = () => {
console.log("🚀 ~ ToggleMuteButton ~ rtc:", rtc)

    const [isMuted, setIsMuted] = useState(false);
const {toggleMuted, setToggleMuted}=useCallStore()
    const toggleMicrophone = async () => {
        setIsMuted(!isMuted);
        setToggleMuted(!isMuted)
        if (rtc.localAudioTrack) {
            if (isMuted) {
                await rtc.localAudioTrack.setEnabled(true);
            } else {
                await rtc.localAudioTrack.setEnabled(false);
            }
        }
    };
    useEffect(()=>{
        setIsMuted(toggleMuted)
    },[])
    return (
        <Tooltip text={isMuted?"Unmute":"mute"}>

        <IconButton onClick={toggleMicrophone} className="h-6 w-6">
            {isMuted ? <MicOff className="h-5 w-5 text-red-500" /> : <Mic className="h-5 w-5 text-white" />}
        </IconButton>
        </Tooltip>
    )
}

export default ToggleMuteButton