import DisconnectIcon from '@/assets/icons/DisconnectIcon';
import IncomingCallIcon from '@/assets/icons/IncomingCallIcon';
import ChatAvatar from '@/components/ChatWindow/ChatAvatar';
import IconButton from '@/components/Common/Button/IconButton';
import useGroupCall from '@/hooks/useGroupCall';
import useAuthStore from '@/store/useAuthStore';
import useCallModalStore from '@/store/useCallModalStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import AgoraRTC from 'agora-rtc-sdk-ng';
import { motion } from 'framer-motion';
import { Mic, Minimize } from 'lucide-react';
import { useRef } from 'react';
import Draggable from 'react-draggable';
import ToggleMuteButton from './component/ToggleMuteButton';

const rtc = {
  client: AgoraRTC.createClient({ mode: 'rtc', codec: 'h264' }),
  localAudioTrack: null,
  remoteAudioTrack: null,
};

function GroupCallPopup() {
  const { setIsMinimized, isMinimized } = useCallModalStore();
  const { handleAcceptCall, handleDeclineCall, isCallActive } = useGroupCall();
  const { voiceCall } = useVoiceCallStore();
  const positionRef = useRef({ x: 0, y: 0 });
  const { userDetails } = useAuthStore();


  // if (!voiceCall) return null;

  // --------------
  // useEffect(() => {
  //   setPosition({ x: 0, y: 0 });
  // }, [voiceCall]);

  const handleMinimized = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsMinimized(true);
  };
  const MinimizedCall = () => (
    <div
      className="fixed bottom-4 right-4 z-50 flex cursor-pointer items-center rounded-full bg-zinc-800 px-5 py-2 shadow-lg transition-transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-green-400"
      onClick={() => setIsMinimized(false)}
      role="button"
      tabIndex={0}
      aria-label="Restore call window"
    >
      <Mic className="h-5 w-5 text-green-400" />
      <span className="text-white ml-3 select-none font-medium">
        Call in Progress
      </span>
    </div>
  );

  const renderParticipants = () => (
    <div className="grid grid-cols-3 gap-6 px-5 py-5">
      {voiceCall?.groupCallMembers.map((user) => (
        <div
          key={user.id}
          className="relative flex cursor-default flex-col items-center rounded-2xl bg-zinc-800 p-4 shadow-md transition-shadow duration-300 hover:shadow-xl"
          aria-label={`${user.username}, ${user.isMuted ? 'Muted' : 'Unmuted'}`}
        >
          <div className="relative h-20 w-20 rounded-full overflow-hidden">
            <ChatAvatar
              profileImage={user.profileImage}
              firstName={user?.firstName}
              lastName={user?.lastName}
              imageClassName="h-full w-full rounded-full object-cover"
              imageWidth={80}
              imageHeight={80}
              avatarSize={80}
            />
            {/* <div className="absolute bottom-1 right-1 rounded-full bg-zinc-900 bg-opacity-75 p-1 flex items-center justify-center">
              {user.isMuted ? (
                <MicOff className="w-5 h-5 text-red-500" aria-label="Muted" />
              ) : (
                <Mic className="w-5 h-5 text-green-400" aria-label="Unmuted" />
              )}
            </div> */}
          </div>
          <p className="mt-3 max-w-[100px] select-text truncate text-center text-sm font-semibold">
            {user.username}
          </p>
        </div>
      ))}
    </div>
  );

  const CallContent = () => (
    <div
      className="text-white relative min-w-[320px] max-w-[420px] select-none overflow-hidden rounded-3xl bg-zinc-900/95 shadow-2xl backdrop-blur-lg"
      role="dialog"
      aria-modal="true"
      // aria-label={isCallActive ? 'Active group call window' : 'Incoming group call'}
      aria-label={voiceCall?.channelName}
    >
      {/* Header */}
      <div
        className="drag-handle flex cursor-move select-none items-center justify-between bg-zinc-800 px-5 py-3"
        aria-describedby="call-status"
      >
        <h3
          id="call-status"
          className="max-w-[280px] truncate text-lg font-semibold"
          // title={isCallActive ? 'Connected' : 'Incoming Group Call'}
          aria-label={voiceCall?.channelName}
        >
          {/* {isCallActive ? 'Connected' : 'Incoming Group Call'} */}
          {voiceCall?.channelName}
        </h3>
        {/* <Tooltip text="Minimize"> */}
        <IconButton
          onClick={handleMinimized}
          className="text-white rounded-full p-1 transition hover:bg-zinc-700 focus:ring-2 focus:ring-green-400"
          aria-label="Minimize call window"
        >
          <Minimize className="h-5 w-5" />
        </IconButton>
        {/* </Tooltip> */}
      </div>

      {/* Timer */}
      {/* {isCallActive && (
        <div className="text-center text-sm py-2 text-gray-300 border-b border-zinc-700 select-text">
          <Timer isActive={isCallActive} />
        </div>
      )} */}

      {/* Participants */}
      {renderParticipants()}

      {/* Call Actions */}
      <div className="flex items-center justify-center gap-6 p-4">
        {userDetails?.userId != voiceCall?.userId && !isCallActive && (
          <IconButton
            onClick={handleAcceptCall}
            className=" h-9 w-9 rounded-full bg-green-600 hover:scale-125"
          >
            <IncomingCallIcon className="text-white h-8 w-8" />
          </IconButton>
        )}
        <IconButton
          onClick={handleDeclineCall}
          className=" h-9 w-9 rotate-[135deg] rounded-full bg-red-600 hover:scale-125"
        >
          <DisconnectIcon className="text-white h-9 w-9 " />
        </IconButton>
        {/* <IconButton onClick={toggleMicrophone} className="h-6 w-6">
            {mutedRef ? <MicOff className="h-5 w-5 text-red-500" /> : <Mic className="h-5 w-5 text-white" />}
          </IconButton> */}
        <ToggleMuteButton rtc={rtc} />
      </div>
    </div>
  );

  return (
    <>
      {isMinimized ? (
        <MinimizedCall />
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center "
          aria-live="polite"
        >
          <Draggable
            handle=".drag-handle"
            bounds="parent"
            defaultPosition={{ x: 0, y: 0 }}
            onStop={(_, data) => {
              positionRef.current = { x: data.x, y: data.y };
            }}
          >
            <div className="m-4 cursor-default">
              <CallContent />
            </div>
          </Draggable>
        </motion.div>
      )}
    </>
  );
}

export default GroupCallPopup;
