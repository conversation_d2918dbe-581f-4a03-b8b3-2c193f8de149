'use client';

import IconButton from '@/components/Common/Button/IconButton';
import Tooltip from '@/components/Common/Tooltip';
import ChatAvatar from '@/components/ChatWindow/ChatAvatar';
import {
  useGetPublicGroupDetails,
  useJoinedGroupMutation,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import useChatStore from '@/store/useChatStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import useDebounce from '@/utils/useDebounce';
import { useQueryClient } from '@tanstack/react-query';
import { UserRoundMinus, UserRoundPlus, Users, X } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import UpdateGroupDetailsModal from '../UpdateGroupDetailsModal';
import AddFriendsFromSearch from './AddFriendsFromSearch';

function ShowGroupModal() {
  const { groupId } = useGroupChatStore((state) => state);
  const { isAuthenticated, userDetails } = useAuthStore((state) => state);
  const {
    showChatHeader,
    setShowChatHeader,
    setShowChat,
    setChatHeaderActiveTab,
  } = useChatStore();

  // const { data: groupDetails } = useGetGroupDetails({
  //   params: { groupId },
  //   enabled: !!isAuthenticated,
  // });
  const { data: groupDetails, isLoading: groupDetailsLoading } =
    useGetPublicGroupDetails({
      params: { groupId },
      enabled: !!isAuthenticated,
    });
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredMembers, setFilteredMembers] = useState(
    groupDetails?.group.members,
  );

  const debouncedSearchTerm = useDebounce(searchTerm, 800); // Apply debounce with 500ms delay
  const { openModal, closeModal } = useModalStore();
  const navigateToGroupPage = () => {
    const groupName = groupDetails?.group.groupName;
    if (groupName) {
      // router.push(`/group/${encodeURIComponent(groupName)}`);
      closeModal(); // Close the modal after navigation
    }
    if (window.innerWidth < 1024) {
      setShowChat(false);
      setShowChatHeader(false);
    }
  };

  const queryClient = useQueryClient();
  const openAddMemberModal = () => {
    openModal(
      <AddFriendsFromSearch
        groupId={groupId}
        groupMembers={groupDetails?.group.members}
        isAdmin={userDetails?.userId == groupDetails?.group?.groupAdmin}
      />,
    );
  };

  const { mutate: updateJoinedGroup } = useJoinedGroupMutation({
    onSuccess: (response) => {
      console.log(response, ':::::::::::::::::response');
      toast.success('Successfully removed from group');

      queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
      closeModal();
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
      toast.error(message);
    },
  });

  // Filter members based on debounced search term
  useEffect(() => {
    if (debouncedSearchTerm) {
      const filtered = groupDetails?.group.members.filter((member) => {
        const { firstName, lastName, username } = member.user;
        return (
          firstName.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          lastName.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          username.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        );
      });
      setFilteredMembers(filtered);
    } else {
      setFilteredMembers(groupDetails?.group.members); // Reset to all members if no search term
    }
  }, [debouncedSearchTerm, groupDetails?.group.members]); // Dependency array to trigger when debounced term changes

  const handleRemoveMember = (userId) => {
    console.log(`Removing member with userId: ${userId}`);
    // Your logic to remove the member
    const payload = {
      groupId,
      action: 'remove',
      members: [userId],
    };

    updateJoinedGroup(payload);
  };

  const openEditGroupDetails = () => {
    openModal(
      <UpdateGroupDetailsModal
        groupId={groupId}
        groupMembers={groupDetails?.group.members}
      />,
    );
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <h3 className="text-white mt-1 text-lg font-semibold">
              Members of {groupDetails?.group.groupName}
            </h3>

            <div className="flex flex-row items-center gap-4">
              {/* onlyAdminCanUpdateGroupDetails	false */}
              <Tooltip text="Details">
                <Link
                  href={`/group/${encodeURIComponent(
                    groupDetails?.group?.groupName,
                  )
                    .replace(/%20/g, '-')
                    .toLowerCase()}`}
                >
                  <IconButton
                    type="button"
                    onClick={navigateToGroupPage}
                    className="h-6 w-6 min-w-6"
                  >
                    <Users className="groupDetails?.group-hover:fill-white-1000 h-5 w-5 fill-steelTeal-1000 transition-all duration-300" />
                  </IconButton>
                </Link>
              </Tooltip>
              {(groupDetails?.group?.groupSettings
                ?.onlyAdminCanUpdateGroupDetails === false ||
                userDetails?.userId == groupDetails?.group?.groupAdmin) && (
                <span onClick={() => openEditGroupDetails(groupDetails?.group)}>
                  Update
                </span>
              )}
              {groupDetails?.group?.members.find(
                (m) => m.userId == userDetails?.userId,
              ) &&
                (groupDetails?.group?.groupSettings?.onlyAdminCanAddMembers ===
                  false ||
                  userDetails?.userId == groupDetails?.group?.groupAdmin) && (
                  <IconButton
                    onClick={() => openAddMemberModal()}
                    className="h-6 w-6 min-w-6"
                  >
                    <UserRoundPlus className="groupDetails?.group-hover:fill-white-1000 h-5 w-5 fill-steelTeal-1000 transition-all duration-300" />
                  </IconButton>
                )}
              <IconButton
                onClick={() => closeModal()}
                className="h-6 w-6 min-w-6"
              >
                <X className="groupDetails?.group-hover:fill-white-1000 h-5 w-5 fill-steelTeal-1000 transition-all duration-300" />
              </IconButton>
            </div>
          </div>
          <div className="px-2 py-1">
            <div className="mb-1 flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
              <div className="relative w-full">
                <input
                  className="h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] pr-8 py-2 placeholder:text-steelTeal-1000"
                  placeholder="Search friends"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {searchTerm && (
                  <span
                    className="absolute right-2 top-1/2 -translate-y-1/2 transform cursor-pointer text-gray-500 hover:text-gray-700"
                    onClick={() => setSearchTerm('')}
                    style={{ fontSize: '16px' }}
                  >
                    &#x2715;
                  </span>
                )}
              </div>
            </div>

            <section className="max-h-[218px] overflow-y-auto pt-1">
              <div className="flex flex-col gap-2">
                {filteredMembers?.length > 0 ? (
                  filteredMembers?.map((member) => {
                    const { firstName, lastName, username, profileImage } =
                      member.user;
                    console.log(member, ':::::member');
                    const userId = member?.userId;
                    const isAdmin = member?.isAdmin;
                    return (
                      <div
                        key={userId}
                        className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
                      >
                        <div className="flex flex-row items-center justify-center gap-2.5">
                          {/* Profile Image */}
                          <div className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full overflow-hidden">
                            <ChatAvatar
                              profileImage={profileImage}
                              firstName={firstName}
                              lastName={lastName}
                              imageClassName="h-full w-full rounded-full object-cover object-center"
                              imageWidth={48}
                              imageHeight={48}
                              avatarSize={48}
                            />
                          </div>

                          {/* User Info (Name and Admin check) */}
                          <div className="mt-4 flex flex-col justify-center">
                            <span className="font-semibold">
                              {firstName} {lastName}
                            </span>
                            {isAdmin && (
                              <span className="text-sm text-green-500">
                                Admin
                              </span>
                            )}
                            <span className="text-sm text-gray-500">
                              @{username}
                            </span>
                          </div>
                        </div>
                        {isAdmin ? (
                          <></>
                        ) : (
                          userDetails?.userId ===
                            groupDetails?.group?.groupAdmin && (
                            <div
                              className="cursor-pointer"
                              onClick={() => handleRemoveMember(userId)}
                            >
                              <UserRoundMinus className="text-white h-6 w-6" />
                            </div>
                          )
                        )}
                      </div>
                    );
                  })
                ) : (
                  <div className="mt-10 flex justify-center">
                    {' '}
                    {groupDetailsLoading ? 'Loading...' : 'No Friends'}{' '}
                  </div>
                )}
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ShowGroupModal;
