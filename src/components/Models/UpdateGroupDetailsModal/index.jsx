'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import IconButton from '@/components/Common/Button/IconButton';
import { zodResolver } from '@hookform/resolvers/zod';
import useModalStore from '@/store/useModalStore';
import { X } from 'lucide-react';
import {
  useGroupChatMutation,
  useGetGroupDetails,
  useGroupChatUpdateDetailsMutation,
} from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import useAuthStore from '@/store/useAuthStore';
import { updateGroupSchema } from '@/schemas/CreateGroupSchema';

function ToggleSwitch({ label, name, register, defaultChecked }) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-white text-base">{label}</span>
      <label className="relative inline-flex cursor-pointer items-center">
        <input
          type="checkbox"
          {...register(name)}
          defaultChecked={defaultChecked}
          className="peer sr-only"
        />
      <div className="peer-focus:ring-steelTeal-300 relative h-6 w-11 rounded-full bg-gray-600 after:absolute after:left-1 after:top-1 after:h-4 after:w-4 after:rounded-full after:border after:border-gray-300 after:bg-gray-300 after:transition-all after:content-[''] peer-checked:bg-red-700 peer-checked:after:translate-x-5 peer-focus:ring-4" />
      </label>
    </div>
  );
}

function UpdateGroupDetailsModal({ groupId }) {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { data: groupDetails, refetch: refetchGroupDetails } =
    useGetGroupDetails({
      params: { groupId },
      enabled: !!isAuthenticated,
    });

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(updateGroupSchema),
    defaultValues: {
      groupName: '',
      groupDescription: '',
      // onlyAdminCanCall: false,
      onlyAdminCanAddMembers: false,
      onlyAdminCanUpdateGroupDetails: false,
    },
  });

  const queryClient = useQueryClient();
  const { closeModal } = useModalStore();

  const groupChatMutation = useGroupChatUpdateDetailsMutation({
    onSuccess: () => {
      queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
      toast.success('Group details updated successfully!');
      closeModal();
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Failed to update group details';
      toast.error(message);
    },
  });

  useEffect(() => {
    if (groupDetails?.group) {
      const { groupName, groupDescription, groupSettings } = groupDetails.group;
      setValue('groupName', groupName);
      setValue('groupDescription', groupDescription);
      // setValue('onlyAdminCanCall', groupSettings.onlyAdminCanCall);
      setValue('onlyAdminCanAddMembers', groupSettings.onlyAdminCanAddMembers);
      setValue(
        'onlyAdminCanUpdateGroupDetails',
        groupSettings.onlyAdminCanUpdateGroupDetails,
      );
    }
  }, [groupDetails, setValue]);

  const onSubmit = async (data) => {
    try {
      // data.onlyAdminCanCall = String(data.onlyAdminCanCall);
      data.onlyAdminCanAddMembers = String(data.onlyAdminCanAddMembers);
      data.onlyAdminCanUpdateGroupDetails = String(
        data.onlyAdminCanUpdateGroupDetails,
      );
      groupChatMutation.mutate({ ...data, groupId });
    } catch (error) {
      toast.error('Failed to update group details!');
    }
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <h3 className="text-white mt-1 text-lg font-semibold">
              Update Group Details
            </h3>
            <IconButton
              onClick={() => closeModal()}
              className="h-6 w-6 min-w-6"
            >
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          <div className="p-4">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="mb-1 text-base font-normal text-steelTeal-1000">
                    Group Name
                  </label>
                  <input
                    type="text"
                    {...register('groupName')}
                    placeholder="Enter group name"
                    className={`w-full rounded-md border ${
                      errors.groupName
                        ? 'border-red-500'
                        : 'border-solid border-richBlack-1000'
                    } text-white bg-richBlack-1000 p-3 focus:border-steelTeal-1000 focus:shadow-inputInsetShadow focus:outline-none`}
                  />
                  {errors.groupName && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.groupName.message}
                    </p>
                  )}
                </div>
                <div>
                  <label className="mb-1 text-base font-normal text-steelTeal-1000">
                    Group Description
                  </label>
                  <textarea
                    {...register('groupDescription')}
                    placeholder="Enter group description"
                    className={`w-full rounded-md border ${
                      errors.groupDescription
                        ? 'border-red-500'
                        : 'border-solid border-richBlack-1000'
                    } text-white bg-richBlack-1000 p-3 focus:border-steelTeal-1000 focus:shadow-inputInsetShadow focus:outline-none`}
                  />
                  {errors.groupDescription && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.groupDescription.message}
                    </p>
                  )}
                </div>
                {/* <ToggleSwitch
                  label="Only Admin Can Call"
                  name="onlyAdminCanCall"
                  register={register}
                  defaultChecked={
                    groupDetails?.group?.groupSettings?.onlyAdminCanCall
                  }
                /> */}
                <ToggleSwitch
                  label="Only Admin Can Add Members"
                  name="onlyAdminCanAddMembers"
                  register={register}
                  defaultChecked={
                    groupDetails?.group?.groupSettings?.onlyAdminCanAddMembers
                  }
                />
                <ToggleSwitch
                  label="Only Admin Can Update Group Details"
                  name="onlyAdminCanUpdateGroupDetails"
                  register={register}
                  defaultChecked={
                    groupDetails?.group?.groupSettings
                      ?.onlyAdminCanUpdateGroupDetails
                  }
                />
              </div>
              <div className="mt-6 flex justify-center">
                <PrimaryButton type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Updating...' : 'Update Group'}
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}

export default UpdateGroupDetailsModal;
