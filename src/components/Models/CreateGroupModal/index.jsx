'use client';

// Add useEffect to check required fields on form submission attempt
import React, { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import IconButton from '@/components/Common/Button/IconButton';
import { zodResolver } from '@hookform/resolvers/zod';
import { CreateGroupSchema } from '@/schemas/CreateGroupSchema';
import useModalStore from '@/store/useModalStore';
import { X, Upload, AlertCircle, ChevronDown, Camera } from 'lucide-react';
import { useGroupChatMutation } from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import MultiSelect from '@/components/ChatSelect';

// File validation constants
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
];

function ToggleSwitch({ label, name, register }) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-white text-base">{label}</span>
      <label className="relative inline-flex cursor-pointer items-center">
        <input type="checkbox" {...register(name)} className="peer sr-only" />
        <div className="peer-focus:ring-steelTeal-300 relative h-6 w-11 rounded-full bg-gray-600 after:absolute after:left-1 after:top-1 after:h-4 after:w-4 after:rounded-full after:border after:border-gray-300 after:bg-gray-300 after:transition-all after:content-[''] peer-checked:bg-red-700 peer-checked:after:translate-x-5 peer-focus:ring-4" />
      </label>
    </div>
  );
}

function CreateGroupModal() {
  const [groupImage, setGroupImage] = useState(null);
  const [groupImagePreview, setGroupImagePreview] = useState(null);
  const [bannerImage, setBannerImage] = useState(null);
  const [bannerPreview, setBannerPreview] = useState(null);
  const [profileError, setProfileError] = useState('');
  const [bannerError, setBannerError] = useState('');
  const [profileRequired, setProfileRequired] = useState(false);
  const [bannerRequired, setBannerRequired] = useState(false);

  const modalContentRef = useRef(null);

  const scrollToTop = () => {
    if (modalContentRef.current) {
      modalContentRef.current.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    if (profileRequired && groupImage) {
      setProfileRequired(false);
      setProfileError('');
    }

    if (bannerRequired && bannerImage) {
      setBannerRequired(false);
      setBannerError('');
    }
  }, [groupImage, bannerImage, profileRequired, bannerRequired]);

  useEffect(() => {
    if (profileError || bannerError) {
      scrollToTop();
    }
  }, [profileError, bannerError]);

  const {
    register,
    watch,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(CreateGroupSchema),
    defaultValues: {
      groupName: '',
      groupDescription: '',
      // onlyAdminCanCall: false,
      onlyAdminCanAddMembers: false,
      // onlySubAdminCanAddMembers: false,
      onlyAdminCanUpdateGroupDetails: true,
      groupType: 'public',
      profile: null,
      groupBanner: null,
    },
  });
  const groupType = watch('groupType');
  const [selectedUsers, setSelectedUsers] = useState([]); // Manage selected users' IDs

  const queryClient = useQueryClient();
  const { closeModal } = useModalStore();

  const groupChatMutation = useGroupChatMutation({
    onSuccess: (response) => {
      toast.success('Group created successfully!');
      queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
      closeModal();
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Failed to sign up';
      toast.error(message);
    },
  });

  const validateImageFile = (file, setErrorFn, isRequired = false) => {
    // Reset any previous errors
    setErrorFn('');

    // Check if file exists
    if (!file) {
      if (isRequired) {
        setErrorFn('This field is required');
        return false;
      }
      return true;
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      setErrorFn('File size should be less than 5MB');
      return false;
    }

    // Check file type
    if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
      setErrorFn('Only JPG, JPEG, PNG and WebP formats are supported');
      return false;
    }

    return true;
  };

  const handleGroupImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (validateImageFile(file, setProfileError)) {
        setGroupImage(file);
        const reader = new FileReader();
        reader.onloadend = () => {
          setGroupImagePreview(reader.result);
        };
        reader.readAsDataURL(file);
      } else {
        // Reset preview if validation fails
        setGroupImage(null);
        setGroupImagePreview(null);
      }
    }
  };

  const handleBannerImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (validateImageFile(file, setBannerError)) {
        setBannerImage(file);
        const reader = new FileReader();
        reader.onloadend = () => {
          setBannerPreview(reader.result);
        };
        reader.readAsDataURL(file);
      } else {
        // Reset preview if validation fails
        setBannerImage(null);
        setBannerPreview(null);
      }
    }
  };

  const onSubmit = async (data) => {
    try {
      let hasErrors = false;

      if (!groupImage) {
        setProfileError('Profile image is required');
        setProfileRequired(true);
        hasErrors = true;
      }
      if (!bannerImage) {
        setBannerError('Banner image is required');
        setBannerRequired(true);
        hasErrors = true;
      }

      const isProfileValid = validateImageFile(
        groupImage,
        setProfileError,
        true,
      );
      const isBannerValid = validateImageFile(
        bannerImage,
        setBannerError,
        true,
      );

      if (!isProfileValid || !isBannerValid) {
        hasErrors = true;
      }

      if (selectedUsers.length === 0) {
        toast.error('Please select at least one member for the group');
        hasErrors = true;
      }

      if (hasErrors) {
        scrollToTop();
        return;
      }

      // Proceed with form submission
      const formData = new FormData();

      formData.append('groupName', data.groupName);
      formData.append('groupDescription', data.groupDescription);
      formData.append(
        'onlyAdminCanAddMembers',
        String(data.onlyAdminCanAddMembers),
      );
      formData.append(
        'onlyAdminCanUpdateGroupDetails',
        String(data.onlyAdminCanUpdateGroupDetails),
      );
      formData.append('groupType', data.groupType);

      selectedUsers.forEach((user, index) => {
        formData.append(`members[${index}]`, user);
      });

      if (bannerImage) {
        formData.append('groupBanner', bannerImage);
      }

      if (groupImage) {
        formData.append('profile', groupImage);
      }

      groupChatMutation.mutate(formData);
    } catch (error) {
      toast.error('Failed to create group!');
    }
  };
  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative max-h-[calc(100%-120px)] w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <h3 className="text-white mt-1 text-lg font-semibold">
              Create Group
            </h3>
            <IconButton
              onClick={() => closeModal()}
              className="h-6 w-6 min-w-6"
            >
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          <div
            ref={modalContentRef}
            className="max-h-[calc(100vh-200px)] overflow-y-auto p-4 max-xl:pb-16"
          >
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="">
                <div className="mb-4 flex items-center gap-4">
                  <div>
                    <label
                      htmlFor="group-img"
                      className={`relative block cursor-pointer ${profileRequired ? 'rounded-full ring-2 ring-red-500' : ''}`}
                    >
                      {groupImagePreview ? (
                        <Image
                          src={groupImagePreview}
                          width={80}
                          height={80}
                          alt="Group Profile"
                          className="bg-white h-20 w-20 rounded-full object-cover object-center"
                        />
                      ) : (
                        <div className="flex h-20 w-20 items-center justify-center rounded-full border-2 border-steelTeal-1000 bg-gray-700">
                          <Camera className="h-8 w-8 text-steelTeal-1000" />
                        </div>
                      )}
                      <input
                        type="file"
                        id="group-img"
                        onChange={handleGroupImageChange}
                        className="hidden"
                        accept="image/png, image/jpeg, image/jpg, image/webp"
                      />
                    </label>
                    {profileError && (
                      <p className="mt-1 max-w-[80px] text-center text-xs text-red-500">
                        {profileError}
                      </p>
                    )}
                  </div>
                  <div className="grow">
                    <label className="mb-1 text-base font-normal text-steelTeal-1000">
                      Enter group name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      {...register('groupName')}
                      placeholder="Enter group name"
                      className={`w-full rounded-md border ${errors.groupName ? 'border-red-500' : 'border-solid border-richBlack-1000'} text-white bg-black-1000 px-3 py-2 focus:border-steelTeal-1000 focus:shadow-inputInsetShadow focus:outline-none`}
                    />
                    {errors.groupName && (
                      <p className="mt-0.5 text-xs text-red-500">
                        {errors.groupName.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Banner Image Upload Section */}
                <div className="mb-4">
                  <label className="mb-1 text-base font-normal text-steelTeal-1000">
                    Group Banner <span className="text-red-500">*</span>
                  </label>
                  <div
                    className={`relative h-32 w-full overflow-hidden rounded-lg border ${bannerRequired ? 'border-red-500 ring-2 ring-red-500' : 'border-richBlack-1000'} bg-black-1000`}
                  >
                    {bannerPreview ? (
                      <Image
                        src={bannerPreview}
                        alt="Banner Preview"
                        layout="fill"
                        objectFit="cover"
                        className="h-full w-full"
                      />
                    ) : (
                      <div className="flex h-full items-center justify-center bg-black-1000">
                        <div className="text-center">
                          <Upload className="mx-auto h-8 w-8 text-steelTeal-1000" />
                          <p className="mt-1 text-sm text-steelTeal-1000">
                            Upload banner image
                          </p>
                        </div>
                      </div>
                    )}
                    <input
                      type="file"
                      id="banner-img"
                      onChange={handleBannerImageChange}
                      className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                      accept="image/png, image/jpeg, image/jpg, image/webp"
                    />
                  </div>
                  {bannerError && (
                    <p className="mt-1 text-xs text-red-500">{bannerError}</p>
                  )}
                </div>

                {/* Group Type Selection */}
                <div className="mb-4">
                  <label className="mb-1 block text-base font-normal text-steelTeal-1000">
                    Group Type <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <select
                      {...register('groupType')}
                      className="text-white w-full appearance-none rounded-md border border-solid border-richBlack-1000 bg-black-1000 px-3 py-2 focus:border-steelTeal-1000 focus:shadow-inputInsetShadow focus:outline-none"
                    >
                      <option value="private">Private</option>
                      <option value="public">Public</option>
                    </select>
                    <div className="text-white pointer-events-none absolute inset-y-0 right-0 flex items-center px-2">
                      <ChevronDown className="h-4 w-4" />
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <label className="mb-1 text-base font-normal text-steelTeal-1000">
                    Group Members <span className="text-red-500">*</span>
                  </label>
                  <MultiSelect
                    className="chatMulti-select"
                    classNamePrefix="chatMulti-inner-select"
                    onChange={setSelectedUsers}
                    value={selectedUsers}
                    isSearchable={true}
                    placeholder="Search and select friends..."
                  />
                  {selectedUsers.length === 0 && (
                    <p className="mt-1 flex items-center text-xs text-yellow-500">
                      <AlertCircle className="mr-1 h-3 w-3" />
                      Please select at least one member
                    </p>
                  )}
                </div>

                <div className="mt-3">
                  <label className="text-base font-normal text-steelTeal-1000">
                    Group Description <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    {...register('groupDescription')}
                    placeholder="Enter group description"
                    className={`w-full rounded-md border ${errors.groupDescription ? 'border-red-500' : 'border-solid border-richBlack-1000'} text-white bg-black-1000 p-3 focus:border-steelTeal-1000 focus:shadow-inputInsetShadow focus:outline-none`}
                  />
                  {errors.groupDescription && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.groupDescription.message}
                    </p>
                  )}
                </div>
                <div className="mt-4 flex flex-col gap-3">
                  {/* <ToggleSwitch
                    label="Only Admin Can Call"
                    name="onlyAdminCanCall"
                    register={register}
                  /> */}
                  <ToggleSwitch
                    label="Only Admin Can Add Members"
                    name="onlyAdminCanAddMembers"
                    register={register}
                  />
                  {/* <ToggleSwitch
                    label="Only Subadmins Can Add Members"
                    name="onlySubAdminCanAddMembers"
                    register={register}
                  /> */}
                  {groupType === 'private' && (
                    <ToggleSwitch
                      label="Only Admin Can Update Group Details"
                      name="onlyAdminCanUpdateGroupDetails"
                      register={register}
                    />
                  )}
                </div>
              </div>
              <div className="mt-6 flex justify-center">
                <PrimaryButton
                  type="submit"
                  disabled={groupChatMutation.isPending || profileError || bannerError}
                >
                  {groupChatMutation.isPending ? (
                    <div className="flex items-center gap-2">
                      <span>Creating...</span>
                    </div>
                  ) : (
                    'Create Group'
                  )}
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CreateGroupModal;
