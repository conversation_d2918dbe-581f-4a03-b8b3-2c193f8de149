import React, { useState, useEffect, useRef } from 'react';
import { X, Star, Crown, Zap, Gift, Loader2 } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import useAuthStore from '@/store/useAuthStore';
import { toast } from 'react-hot-toast';
import { usePaymentDepositMutation } from '@/reactQuery/paymentQuery';
import {
  loadPraxisSDK,
  createPraxisPaymentConfig,
  initializePraxisPayment,
  validatePraxisConfig,
  testPraxisCredentials
} from '@/utils/praxisConfig';

const packagesData = [
  {
    id: 1,
    packageId: 1,
    amount: 9.99,
    name: 'Starter Pack',
    price: '$9.99',
    originalPrice: '$19.99',
    discount: '50% OFF',
    icon: Gift,
    color: 'from-blue-500 to-blue-600',
    features: [
      '100 Gold Coins',
      '50 Silver Coins',
      'Basic Support',
      '7 Days Access'
    ],
    popular: false
  },
  {
    id: 2,
    packageId: 2,
    amount: 24.99,
    name: 'Premium Pack',
    price: '$24.99',
    originalPrice: '$49.99',
    discount: '50% OFF',
    icon: Star,
    color: 'from-purple-500 to-purple-600',
    features: [
      '500 Gold Coins',
      '250 Silver Coins',
      'Priority Support',
      '30 Days Access',
    ],
    popular: true
  },
  {
    id: 3,
    packageId: 3,
    amount: 49.99,
    name: 'VIP Pack',
    price: '$49.99',
    originalPrice: '$99.99',
    discount: '50% OFF',
    icon: Crown,
    color: 'from-yellow-500 to-yellow-600',
    features: [
      '1000 Gold Coins',
      '500 Silver Coins',
      'VIP Support',
      '90 Days Access',
    ],
    popular: false
  },
  {
    id: 4,
    packageId: 4,
    amount: 99.99,
    name: 'Ultimate Pack',
    price: '$99.99',
    originalPrice: '$199.99',
    discount: '50% OFF',
    icon: Zap,
    color: 'from-red-500 to-red-600',
    features: [
      '2500 Gold Coins',
      '1250 Silver Coins',
      '24/7 VIP Support',
      '365 Days Access',
    ],
    popular: false
  }
];

function PackagesModal() {
  const { closeModal } = useModalStore();
  const { userDetails } = useAuthStore();
  const [loadingPackageId, setLoadingPackageId] = useState(null);

  // Praxis state
  const [praxisLoaded, setPraxisLoaded] = useState(false);
  const [praxisError, setPraxisError] = useState(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('card');
  const praxisRef = useRef(null);

  // Initialize Praxis SDK
  useEffect(() => {
    const initializePraxis = async () => {
      try {
        console.log('🚀 Initializing Praxis payment system...');

        // Test credentials first
        const credentialsValid = testPraxisCredentials();
        if (!credentialsValid) {
          setPraxisError('Invalid Praxis credentials');
          setPraxisLoaded(false);
          return;
        }

        // Validate configuration
        const validation = validatePraxisConfig();
        if (!validation.isValid) {
          console.error('❌ Praxis configuration errors:', validation.errors);
          setPraxisError(`Configuration error: ${validation.errors.join(', ')}`);
          setPraxisLoaded(false);
          return;
        }

        // Load Praxis SDK
        await loadPraxisSDK();
        setPraxisLoaded(true);
        setPraxisError(null);
        console.log('✅ Praxis SDK loaded successfully');

      } catch (error) {
        console.error('❌ Failed to load Praxis SDK:', error);
        setPraxisError(error.message);
        setPraxisLoaded(false);
      }
    };

    initializePraxis();

    // Cleanup function
    return () => {
      if (praxisRef.current) {
        try {
          praxisRef.current.close();
        } catch (error) {
          console.log('Error closing Praxis cashier:', error);
        }
      }
    };
  }, []);

  const paymentMutation = usePaymentDepositMutation({
    onSuccess: (data) => {
      toast.success('Payment successful! Your coins have been added.');
      console.log('Payment successful:', data);
      setLoadingPackageId(null);
      closeModal();
    },
    onError: (error) => {
      toast.error(error.message || 'Payment failed. Please try again.');
      console.error('Payment error:', error);
      setLoadingPackageId(null);
    },
  });

  const handleBuyNow = async (packageData) => {
    console.log('🚀 handleBuyNow called with package:', packageData);
    console.log('👤 User details:', userDetails);
    console.log('💳 Selected payment method:', selectedPaymentMethod);
    console.log('🔧 Praxis loaded:', praxisLoaded);
    console.log('🪟 Window.PraxisCashier:', !!window.PraxisCashier);

    if (!userDetails?.userId) {
      toast.error('Please login to purchase packages');
      return;
    }

    setLoadingPackageId(packageData.id);

    // Safety timeout to prevent stuck loading state
    const safetyTimeout = setTimeout(() => {
      console.warn('⏰ Payment timeout - resetting loading state');
      setLoadingPackageId(null);
      toast.error('Payment timed out. Please try again.');
    }, 30000); // 30 seconds timeout

    // Function to clear timeout and loading state
    const clearLoadingState = () => {
      clearTimeout(safetyTimeout);
      setLoadingPackageId(null);
    };

    // Check if Praxis is available and properly configured
    if (praxisLoaded && window.PraxisCashier) {
      console.log('💳 Using Praxis payment system');
      try {
        // Create Praxis payment configuration
        console.log('🔧 Creating Praxis payment configuration...');
        const praxisConfig = createPraxisPaymentConfig({
          packageData,
          userDetails,
          paymentMethod: selectedPaymentMethod,
          onSuccess: (response) => {
            console.log('✅ Praxis payment success:', response);
            toast.success('Payment successful! Your coins have been added.');
            clearLoadingState();

            // Call backend API to process the successful payment
            const apiPayload = {
              amount: packageData.amount,
              userId: userDetails.userId,
              packageId: packageData.packageId,
              paymentMethod: selectedPaymentMethod,
              praxisTransactionId: response.transaction_id,
              praxisOrderId: response.order_id,
              status: 'completed',
              transactionType: 'praxis_payment',
            };

            console.log('📤 Sending payment data to backend API:', apiPayload);
            paymentMutation.mutate(apiPayload);
          },

          onFailure: (error) => {
            console.error('❌ Praxis payment failed:', error);
            toast.error(`Payment failed: ${error.message || 'Unknown error'}`);
            clearLoadingState();
          },

          onPending: (response) => {
            console.log('⏳ Praxis payment pending:', response);
            toast.info('Payment is being processed. Please wait...');
          },

          onCancel: () => {
            console.log('🚫 Praxis payment cancelled');
            toast.info('Payment cancelled.');
            clearLoadingState();
          },
        });

        // Initialize Praxis payment
        console.log('🚀 Initializing Praxis payment with config:', praxisConfig);
        const cashier = await initializePraxisPayment(praxisConfig);
        praxisRef.current = cashier;
        console.log('✅ Praxis cashier initialized:', cashier);

        // Open payment modal
        console.log('🔓 Opening Praxis payment modal...');
        cashier.open();
        console.log('✅ Praxis payment modal opened');

      } catch (error) {
        console.error('❌ Error initializing Praxis payment:', error);
        toast.error('Failed to initialize payment. Please try again.');
        clearLoadingState();
      }
    } else {
      // Fallback to direct API payment
      console.log('🔄 Using direct API payment (Praxis not available)');
      console.log('🔧 Praxis loaded:', praxisLoaded);
      console.log('🪟 PraxisCashier available:', !!window.PraxisCashier);
      console.log('❌ Praxis error:', praxisError);

      const directPaymentData = {
        amount: packageData.amount,
        userId: userDetails.userId,
        packageId: packageData.packageId,
        paymentMethod: selectedPaymentMethod,
        transactionType: 'direct_api',
        status: 'pending',
      };

      console.log('📤 Processing direct payment:', directPaymentData);
      paymentMutation.mutate(directPaymentData);
    }
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black bg-opacity-50"
    >
      <div className="relative max-h-[90vh] w-full max-w-6xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-borderColor-100 p-6">
            <h2 className="text-2xl font-bold text-white-1000">
              Choose Your Package
            </h2>
            {/* Debug Info */}
            <div className="text-xs text-gray-500 mt-2">
              Praxis: {praxisLoaded ? '✅ Loaded' : '❌ Not Loaded'} |
              SDK: {window.PraxisCashier ? '✅ Available' : '❌ Missing'} |
              Error: {praxisError || 'None'}
            </div>
            <button
              type="button"
              onClick={closeModal}
              className="rounded-lg p-2 text-gray-400 hover:bg-gray-700 hover:text-white"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Packages Grid */}
          <div className="p-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
              {packagesData.map((pkg) => {
                const IconComponent = pkg.icon;
                return (
                  <div
                    key={pkg.id}
                    className={`relative rounded-xl border-2 p-6 transition-all duration-300 hover:scale-105 ${
                      pkg.popular
                        ? 'border-yellow-500 bg-gradient-to-br from-yellow-500/10 to-yellow-600/10'
                        : 'border-borderColor-100 bg-richBlack-1000'
                    }`}
                  >
                    {/* Popular Badge */}
                    {pkg.popular && (
                      <div className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-yellow-500 px-4 py-1 text-xs font-bold text-black">
                        MOST POPULAR
                      </div>
                    )}

                    {/* Package Icon */}
                    <div className={`mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r ${pkg.color}`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>

                    {/* Package Name */}
                    <h3 className="mb-2 text-xl font-bold text-white-1000">
                      {pkg.name}
                    </h3>

                    {/* Discount Badge */}
                    <div className="mb-3 inline-block rounded-md bg-green-500 px-2 py-1 text-xs font-bold text-white">
                      {pkg.discount}
                    </div>

                    {/* Pricing */}
                    <div className="mb-4">
                      <div className="flex items-baseline gap-2">
                        <span className="text-3xl font-bold text-white-1000">
                          {pkg.price}
                        </span>
                        <span className="text-lg text-gray-400 line-through">
                          {pkg.originalPrice}
                        </span>
                      </div>
                    </div>

                    {/* Features */}
                    <ul className="mb-6 space-y-2">
                      {pkg.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm text-gray-300">
                          <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>

                    {/* Buy Button */}
                    <button
                      onClick={() => handleBuyNow(pkg)}
                      disabled={loadingPackageId === pkg.id}
                      className={`w-full rounded-lg py-3 font-bold text-white transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 ${
                        pkg.popular
                          ? 'bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700'
                          : `bg-gradient-to-r ${pkg.color} hover:opacity-90`
                      }`}
                    >
                      {loadingPackageId === pkg.id ? (
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Processing...</span>
                        </div>
                      ) : (
                        'Buy Now'
                      )}
                    </button>
                  </div>
                );
              })}
            </div>

            {/* Footer Note */}
            <div className="mt-8 rounded-lg bg-richBlack-1000 p-4 text-center">
              <p className="text-sm text-gray-400">
                🔒 Secure payment • 💰 Money-back guarantee • 🎁 Instant delivery
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PackagesModal;
