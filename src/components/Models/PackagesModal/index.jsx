import React, { useState } from 'react';
import { X, Star, Crown, Zap, Gift, Loader2 } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import useAuthStore from '@/store/useAuthStore';
import { useMutation } from '@tanstack/react-query';
import { getAccessToken } from '@/utils/helper';
import { toast } from 'react-hot-toast';

const packagesData = [
  {
    id: 1,
    packageId: 1,
    amount: 9.99,
    name: 'Starter Pack',
    price: '$9.99',
    originalPrice: '$19.99',
    discount: '50% OFF',
    icon: Gift,
    color: 'from-blue-500 to-blue-600',
    features: [
      '100 Gold Coins',
      '50 Silver Coins',
      'Basic Support',
      '7 Days Access'
    ],
    popular: false
  },
  {
    id: 2,
    packageId: 2,
    amount: 24.99,
    name: 'Premium Pack',
    price: '$24.99',
    originalPrice: '$49.99',
    discount: '50% OFF',
    icon: Star,
    color: 'from-purple-500 to-purple-600',
    features: [
      '500 Gold Coins',
      '250 Silver Coins',
      'Priority Support',
      '30 Days Access',
    ],
    popular: true
  },
  {
    id: 3,
    packageId: 3,
    amount: 49.99,
    name: 'VIP Pack',
    price: '$49.99',
    originalPrice: '$99.99',
    discount: '50% OFF',
    icon: Crown,
    color: 'from-yellow-500 to-yellow-600',
    features: [
      '1000 Gold Coins',
      '500 Silver Coins',
      'VIP Support',
      '90 Days Access',
    ],
    popular: false
  },
  {
    id: 4,
    packageId: 4,
    amount: 99.99,
    name: 'Ultimate Pack',
    price: '$99.99',
    originalPrice: '$199.99',
    discount: '50% OFF',
    icon: Zap,
    color: 'from-red-500 to-red-600',
    features: [
      '2500 Gold Coins',
      '1250 Silver Coins',
      '24/7 VIP Support',
      '365 Days Access',
    ],
    popular: false
  }
];

const processPayment = async (paymentData) => {
  const accessToken = getAccessToken();

  const response = await fetch('https://dev-api.fansbets.us/api/v1/payment-merchant/praxis/deposit', {
    method: 'POST',
    headers: {
      'accept': 'application/json, text/plain, */*',
      'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
      'accesstoken': accessToken,
      'content-type': 'application/json',
      'origin': 'https://dev.fansbets.us',
      'referer': 'https://dev.fansbets.us/',
    },
    body: JSON.stringify(paymentData),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Payment failed');
  }

  return response.json();
};

function PackagesModal() {
  const { closeModal } = useModalStore();
  const { userDetails } = useAuthStore();
  const [loadingPackageId, setLoadingPackageId] = useState(null);

  const paymentMutation = useMutation({
    mutationFn: processPayment,
    onSuccess: (data) => {
      toast.success('Payment successful! Your coins have been added.');
      console.log('Payment successful:', data);
      setLoadingPackageId(null);
      closeModal();
    },
    onError: (error) => {
      toast.error(error.message || 'Payment failed. Please try again.');
      console.error('Payment error:', error);
      setLoadingPackageId(null);
    },
  });

  const handleBuyNow = (packageData) => {
    if (!userDetails?.userId) {
      toast.error('Please login to purchase packages');
      return;
    }

    setLoadingPackageId(packageData.id);

    const paymentData = {
      amount: packageData.amount,
      userId: userDetails.userId,
      packageId: packageData.packageId,
    };

    console.log('Processing payment:', paymentData);
    paymentMutation.mutate(paymentData);
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black bg-opacity-50"
    >
      <div className="relative max-h-[90vh] w-full max-w-6xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-borderColor-100 p-6">
            <h2 className="text-2xl font-bold text-white-1000">
              Choose Your Package
            </h2>
            <button
              type="button"
              onClick={closeModal}
              className="rounded-lg p-2 text-gray-400 hover:bg-gray-700 hover:text-white"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Packages Grid */}
          <div className="p-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
              {packagesData.map((pkg) => {
                const IconComponent = pkg.icon;
                return (
                  <div
                    key={pkg.id}
                    className={`relative rounded-xl border-2 p-6 transition-all duration-300 hover:scale-105 ${
                      pkg.popular
                        ? 'border-yellow-500 bg-gradient-to-br from-yellow-500/10 to-yellow-600/10'
                        : 'border-borderColor-100 bg-richBlack-1000'
                    }`}
                  >
                    {/* Popular Badge */}
                    {pkg.popular && (
                      <div className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-yellow-500 px-4 py-1 text-xs font-bold text-black">
                        MOST POPULAR
                      </div>
                    )}

                    {/* Package Icon */}
                    <div className={`mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r ${pkg.color}`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>

                    {/* Package Name */}
                    <h3 className="mb-2 text-xl font-bold text-white-1000">
                      {pkg.name}
                    </h3>

                    {/* Discount Badge */}
                    <div className="mb-3 inline-block rounded-md bg-green-500 px-2 py-1 text-xs font-bold text-white">
                      {pkg.discount}
                    </div>

                    {/* Pricing */}
                    <div className="mb-4">
                      <div className="flex items-baseline gap-2">
                        <span className="text-3xl font-bold text-white-1000">
                          {pkg.price}
                        </span>
                        <span className="text-lg text-gray-400 line-through">
                          {pkg.originalPrice}
                        </span>
                      </div>
                    </div>

                    {/* Features */}
                    <ul className="mb-6 space-y-2">
                      {pkg.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm text-gray-300">
                          <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>

                    {/* Buy Button */}
                    <button
                      onClick={() => handleBuyNow(pkg)}
                      disabled={loadingPackageId === pkg.id}
                      className={`w-full rounded-lg py-3 font-bold text-white transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 ${
                        pkg.popular
                          ? 'bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700'
                          : `bg-gradient-to-r ${pkg.color} hover:opacity-90`
                      }`}
                    >
                      {loadingPackageId === pkg.id ? (
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Processing...</span>
                        </div>
                      ) : (
                        'Buy Now'
                      )}
                    </button>
                  </div>
                );
              })}
            </div>

            {/* Footer Note */}
            <div className="mt-8 rounded-lg bg-richBlack-1000 p-4 text-center">
              <p className="text-sm text-gray-400">
                🔒 Secure payment • 💰 Money-back guarantee • 🎁 Instant delivery
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PackagesModal;
