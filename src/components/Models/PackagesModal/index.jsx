import React, { useState, useEffect, useRef } from 'react';
import { X, Star, Crown, Zap, Gift, Loader2, Shield, Clock, CreditCard } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import useAuthStore from '@/store/useAuthStore';
import { toast } from 'react-hot-toast';
import { usePaymentDepositMutation } from '@/reactQuery/paymentQuery';
import {
  loadPraxisSDK,
  createPraxisPaymentConfig,
  initializePraxisPayment,
  validatePraxisConfig,
  testPraxisCredentials,
  testSDKUrls,
  validatePaymentData,
  getAvailablePaymentMethods,
  formatPraxisAmount
} from '@/utils/praxisConfig';

// Enhanced payment methods with better descriptions and regional support
const paymentMethods = [
  {
    id: 'card',
    name: 'Credit/Debit Card',
    icon: '💳',
    description: 'Visa, Mastercard, American Express',
    processingTime: 'Instant',
    fees: 'Standard fees apply',
    popular: true
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    icon: '🏦',
    description: 'Direct bank transfer',
    processingTime: '1-3 business days',
    fees: 'Low fees',
    popular: false
  },
  {
    id: 'crypto',
    name: 'Cryptocurrency',
    icon: '₿',
    description: 'Bitcoin, Ethereum, USDT',
    processingTime: '5-30 minutes',
    fees: 'Network fees only',
    popular: true
  },
  {
    id: 'e_wallet',
    name: 'E-Wallet',
    icon: '📱',
    description: 'PayPal, Skrill, Neteller',
    processingTime: 'Instant',
    fees: 'Low fees',
    popular: false
  },
  {
    id: 'voucher',
    name: 'Voucher',
    icon: '🎫',
    description: 'Prepaid vouchers',
    processingTime: 'Instant',
    fees: 'No additional fees',
    popular: false
  }
];

const packagesData = [
  {
    id: 1,
    packageId: 1,
    amount: 9.99,
    name: 'Starter Pack',
    price: '$9.99',
    originalPrice: '$19.99',
    discount: '50% OFF',
    icon: Gift,
    color: 'from-blue-500 to-blue-600',
    features: [
      '100 Gold Coins',
      '50 Silver Coins',
      'Basic Support',
      '7 Days Access'
    ],
    popular: false,
    badge: 'Best Value'
  },
  {
    id: 2,
    packageId: 2,
    amount: 24.99,
    name: 'Premium Pack',
    price: '$24.99',
    originalPrice: '$49.99',
    discount: '50% OFF',
    icon: Star,
    color: 'from-purple-500 to-purple-600',
    features: [
      '500 Gold Coins',
      '250 Silver Coins',
      'Priority Support',
      '30 Days Access',
      'Bonus Rewards'
    ],
    popular: true,
    badge: 'Most Popular'
  },
  {
    id: 3,
    packageId: 3,
    amount: 49.99,
    name: 'VIP Pack',
    price: '$49.99',
    originalPrice: '$99.99',
    discount: '50% OFF',
    icon: Crown,
    color: 'from-yellow-500 to-yellow-600',
    features: [
      '1000 Gold Coins',
      '500 Silver Coins',
      'VIP Support',
      '90 Days Access',
      'Exclusive Rewards'
    ],
    popular: false,
    badge: 'Premium'
  },
  {
    id: 4,
    packageId: 4,
    amount: 99.99,
    name: 'Ultimate Pack',
    price: '$99.99',
    originalPrice: '$199.99',
    discount: '50% OFF',
    icon: Zap,
    color: 'from-red-500 to-red-600',
    features: [
      '2500 Gold Coins',
      '1250 Silver Coins',
      '24/7 VIP Support',
      '365 Days Access',
      'All Premium Features'
    ],
    popular: false,
    badge: 'Ultimate'
  }
];

function PackagesModal() {
  const { closeModal } = useModalStore();
  const { userDetails } = useAuthStore();
  const [loadingPackageId, setLoadingPackageId] = useState(null);
  const [praxisLoaded, setPraxisLoaded] = useState(false);
  const [praxisError, setPraxisError] = useState(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('card');
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState(paymentMethods);
  const [showPaymentDetails, setShowPaymentDetails] = useState(false);
  const praxisRef = useRef(null);

  // Load Praxis SDK and validate configuration
  useEffect(() => {
    const initializePraxis = async () => {
      try {
        console.log('🚀 Initializing Praxis payment system...');

        // Test credentials first
        const credentialsValid = testPraxisCredentials();
        if (!credentialsValid) {
          setPraxisError('Invalid Praxis credentials');
          setPraxisLoaded(false);
          return;
        }

        // Test SDK URLs
        testSDKUrls();

        // Validate configuration
        const validation = validatePraxisConfig();
        if (!validation.isValid) {
          console.error('❌ Praxis configuration errors:', validation.errors);
          setPraxisError(`Configuration error: ${validation.errors.join(', ')}`);
          setPraxisLoaded(false);
          return;
        }

        // Load Praxis SDK with detailed error handling
        try {
          await loadPraxisSDK();
          setPraxisLoaded(true);
          setPraxisError(null);
          console.log('✅ Praxis SDK loaded successfully');
        } catch (sdkError) {
          console.error('❌ SDK loading failed:', sdkError.message);
          setPraxisError(`SDK loading failed: ${sdkError.message}`);
          setPraxisLoaded(false);
          // Don't return here - let it fall back to direct API
        }

        // Get available payment methods based on user's region
        const userRegion = userDetails?.country ? getUserRegion(userDetails.country) : 'global';
        const availableMethods = getAvailablePaymentMethods(userRegion);
        const filteredMethods = paymentMethods.filter(method => 
          availableMethods.includes(method.id)
        );
        setAvailablePaymentMethods(filteredMethods);

      } catch (error) {
        console.error('❌ Failed to load Praxis SDK:', error);
        setPraxisError(error.message);
        setPraxisLoaded(false);
      }
    };

    initializePraxis();

    // Cleanup function
    return () => {
      if (praxisRef.current) {
        try {
          praxisRef.current.close();
        } catch (error) {
          console.log('Error closing Praxis cashier:', error);
        }
      }
    };
  }, [userDetails]);

  // Helper function to determine user's region
  const getUserRegion = (country) => {
    const europeanCountries = ['DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 'SE', 'NO', 'DK', 'FI'];
    const asianCountries = ['CN', 'JP', 'KR', 'IN', 'TH', 'SG', 'MY', 'ID', 'PH', 'VN'];
    const americanCountries = ['US', 'CA', 'MX', 'BR', 'AR', 'CL', 'CO', 'PE'];
    
    if (europeanCountries.includes(country)) return 'europe';
    if (asianCountries.includes(country)) return 'asia';
    if (americanCountries.includes(country)) return 'americas';
    return 'global';
  };

  const paymentMutation = usePaymentDepositMutation({
    onSuccess: (data) => {
      toast.success('Payment successful! Your coins have been added.');
      console.log('Payment successful:', data);
      setLoadingPackageId(null);
      closeModal();
    },
    onError: (error) => {
      toast.error(error.message || 'Payment failed. Please try again.');
      console.error('Payment error:', error);
      setLoadingPackageId(null);
    },
  });

  const handleBuyNow = async (packageData) => {
    if (!userDetails?.userId) {
      toast.error('Please login to purchase packages');
      return;
    }

    // Validate payment data
    const paymentData = {
      amount: packageData.amount,
      userId: userDetails.userId,
      packageId: packageData.packageId,
      paymentMethod: selectedPaymentMethod
    };

    const validation = validatePaymentData(paymentData);
    if (!validation.isValid) {
      toast.error(`Invalid payment data: ${validation.errors.join(', ')}`);
      return;
    }

    setLoadingPackageId(packageData.id);

    // Check if Praxis is available and properly configured
    if (praxisLoaded && window.PraxisCashier) {
      console.log('💳 Using Praxis payment system');
      try {
        // Create Praxis payment configuration
        const praxisConfig = createPraxisPaymentConfig({
          packageData,
          userDetails,
          paymentMethod: selectedPaymentMethod,
          onSuccess: (response) => {
            console.log('✅ Praxis payment success:', response);
            toast.success('Payment successful! Your coins have been added.');
            setLoadingPackageId(null);

            // Call backend API to process the successful payment
            const apiPayload = {
              amount: packageData.amount,
              userId: +userDetails.userId,
              packageId: packageData.packageId,
              paymentMethod: selectedPaymentMethod,
              praxisTransactionId: response.transactionId,
              praxisOrderId: response.orderId,
              status: 'completed',
              transactionType: 'praxis_payment',
            };

            console.log('📤 Sending payment data to backend API:', apiPayload);
            paymentMutation.mutate(apiPayload);
          },

          onFailure: (error) => {
            console.error('❌ Praxis payment failed:', error);
            toast.error(`Payment failed: ${error.message || 'Unknown error'}`);
            setLoadingPackageId(null);
          },

          onPending: (response) => {
            console.log('⏳ Praxis payment pending:', response);
            toast.info('Payment is being processed. Please wait...');
          },

          onCancel: () => {
            console.log('🚫 Praxis payment cancelled');
            toast.info('Payment cancelled.');
            setLoadingPackageId(null);
          },
        });

        // Initialize Praxis payment
        const cashier = await initializePraxisPayment(praxisConfig);
        praxisRef.current = cashier;

        // Open payment modal
        cashier.open();

      } catch (error) {
        console.error('❌ Error initializing Praxis payment:', error);
        toast.error('Failed to initialize payment. Please try again.');
        setLoadingPackageId(null);
      }
    } else {
      // Fallback to direct API payment
      console.log('🔄 Using direct API payment (Praxis not available)');

      const directPaymentData = {
        amount: packageData.amount,
        userId: +userDetails.userId,
        packageId: packageData.packageId,
        paymentMethod: selectedPaymentMethod,
        transactionType: 'direct_api',
        status: 'pending',
      };

      console.log('📤 Processing direct payment:', directPaymentData);
      paymentMutation.mutate(directPaymentData);
    }
  };

  const selectedMethod = availablePaymentMethods.find(m => m.id === selectedPaymentMethod);

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black bg-opacity-50"
    >
      <div className="relative max-h-[90vh] w-full max-w-6xl p-4 overflow-y-auto">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-borderColor-100 p-6">
            <div>
              <h2 className="text-2xl font-bold text-white-1000">
                Choose Your Package
              </h2>
              <p className="text-sm text-gray-400 mt-1">
                Select a package and payment method to get started
              </p>
            </div>
            <button
              type="button"
              onClick={closeModal}
              className="rounded-lg p-2 text-gray-400 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Praxis Status Indicator */}
          {praxisError && (
            <div className="border-b border-borderColor-100 p-4 bg-red-900/20">
              <div className="flex items-center gap-2 text-red-400">
                <Shield className="h-4 w-4" />
                <span className="text-sm">Payment Gateway: {praxisError}</span>
              </div>
            </div>
          )}

          {/* Payment Method Selection */}
          <div className="border-b border-borderColor-100 p-6">
            <h3 className="mb-4 text-lg font-semibold text-white-1000">
              Select Payment Method
            </h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {availablePaymentMethods.map((method) => (
                <button
                  key={method.id}
                  onClick={() => setSelectedPaymentMethod(method.id)}
                  className={`relative flex items-center rounded-lg border-2 p-4 transition-all duration-200 hover:scale-105 ${
                    selectedPaymentMethod === method.id
                      ? 'border-yellow-500 bg-yellow-500/10 text-yellow-500'
                      : 'border-borderColor-100 bg-richBlack-1000 text-gray-300 hover:border-gray-500'
                  }`}
                >
                  {method.popular && (
                    <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                      Popular
                    </div>
                  )}
                  <div className="flex items-center gap-3 w-full">
                    <span className="text-2xl">{method.icon}</span>
                    <div className="flex-1 text-left">
                      <div className="font-medium">{method.name}</div>
                      <div className="text-xs text-gray-400">{method.description}</div>
                      <div className="flex items-center gap-2 mt-1">
                        <Clock className="h-3 w-3" />
                        <span className="text-xs">{method.processingTime}</span>
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            {/* Payment Method Details */}
            {selectedMethod && (
              <div className="mt-4 p-4 bg-richBlack-1000 rounded-lg border border-borderColor-100">
                <div className="flex items-center gap-2 mb-2">
                  <CreditCard className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium text-white-1000">
                    {selectedMethod.name} Details
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Processing Time:</span>
                    <span className="text-white-1000 ml-2">{selectedMethod.processingTime}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Fees:</span>
                    <span className="text-white-1000 ml-2">{selectedMethod.fees}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Packages Grid */}
          <div className="p-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
              {packagesData.map((pkg) => {
                const IconComponent = pkg.icon;
                return (
                  <div
                    key={pkg.id}
                    className={`relative rounded-xl border-2 p-6 transition-all duration-300 hover:scale-105 ${
                      pkg.popular
                        ? 'border-yellow-500 bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 shadow-lg shadow-yellow-500/20'
                        : 'border-borderColor-100 bg-richBlack-1000 hover:border-gray-500'
                    }`}
                  >
                    {/* Badge */}
                    {pkg.popular && (
                      <div className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-yellow-500 px-4 py-1 text-xs font-bold text-black">
                        {pkg.badge}
                      </div>
                    )}

                    {/* Package Icon */}
                    <div className={`mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r ${pkg.color} shadow-lg`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>

                    {/* Package Name */}
                    <h3 className="mb-2 text-xl font-bold text-white-1000">
                      {pkg.name}
                    </h3>

                    {/* Discount Badge */}
                    <div className="mb-3 inline-block rounded-md bg-green-500 px-3 py-1 text-xs font-bold text-white shadow-sm">
                      {pkg.discount}
                    </div>

                    {/* Pricing */}
                    <div className="mb-4">
                      <div className="flex items-baseline gap-2">
                        <span className="text-3xl font-bold text-white-1000">
                          {pkg.price}
                        </span>
                        <span className="text-lg text-gray-400 line-through">
                          {pkg.originalPrice}
                        </span>
                      </div>
                      <div className="text-sm text-gray-400 mt-1">
                        {formatPraxisAmount(pkg.amount)} with {selectedMethod?.name}
                      </div>
                    </div>

                    {/* Features */}
                    <ul className="mb-6 space-y-2">
                      {pkg.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm text-gray-300">
                          <div className="mr-2 h-2 w-2 rounded-full bg-green-500 flex-shrink-0"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>

                    {/* Buy Button */}
                    <button
                      onClick={() => handleBuyNow(pkg)}
                      disabled={loadingPackageId === pkg.id}
                      className={`w-full rounded-lg py-3 font-bold text-white transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 shadow-lg ${
                        pkg.popular
                          ? 'bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 shadow-yellow-500/25'
                          : `bg-gradient-to-r ${pkg.color} hover:opacity-90`
                      }`}
                    >
                      {loadingPackageId === pkg.id ? (
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Processing...</span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center gap-2">
                          <span>{selectedMethod?.icon || '💳'}</span>
                          <span>Buy Now</span>
                          {!praxisLoaded && (
                            <span className="text-xs opacity-75">(Direct API)</span>
                          )}
                        </div>
                      )}
                    </button>

                    {/* Value Indicator */}
                    {pkg.popular && (
                      <div className="mt-3 text-center">
                        <div className="inline-flex items-center gap-1 text-xs text-yellow-500">
                          <Star className="h-3 w-3 fill-current" />
                          <span>Best Value</span>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Payment Security & Info */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Selected Payment Method Summary */}
              <div className="rounded-lg bg-richBlack-1000 p-6 border border-borderColor-100">
                <h4 className="text-lg font-semibold text-white-1000 mb-4">
                  Payment Summary
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Payment Method:</span>
                    <div className="flex items-center gap-2">
                      <span>{selectedMethod?.icon}</span>
                      <span className="text-white-1000">{selectedMethod?.name}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Processing Time:</span>
                    <span className="text-white-1000">{selectedMethod?.processingTime}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Fees:</span>
                    <span className="text-white-1000">{selectedMethod?.fees}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Payment Gateway:</span>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${praxisLoaded ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                      <span className="text-white-1000">
                        {praxisLoaded ? 'Praxis (Secure)' : 'Direct API'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Security Features */}
              <div className="rounded-lg bg-richBlack-1000 p-6 border border-borderColor-100">
                <h4 className="text-lg font-semibold text-white-1000 mb-4">
                  Security & Guarantees
                </h4>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Shield className="h-5 w-5 text-green-500" />
                    <span className="text-sm text-gray-300">SSL Encrypted Payments</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Shield className="h-5 w-5 text-green-500" />
                    <span className="text-sm text-gray-300">PCI DSS Compliant</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Gift className="h-5 w-5 text-blue-500" />
                    <span className="text-sm text-gray-300">Instant Coin Delivery</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-yellow-500" />
                    <span className="text-sm text-gray-300">24/7 Support Available</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer Note */}
            <div className="mt-8 rounded-lg bg-gradient-to-r from-blue-900/20 to-purple-900/20 p-6 border border-borderColor-100">
              <div className="text-center">
                <h5 className="text-lg font-semibold text-white-1000 mb-2">
                  Why Choose Our Packages?
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div className="flex items-center justify-center gap-2">
                    <Shield className="h-5 w-5 text-green-500" />
                    <span className="text-sm text-gray-300">Secure Payments</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-500" />
                    <span className="text-sm text-gray-300">Instant Delivery</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Gift className="h-5 w-5 text-blue-500" />
                    <span className="text-sm text-gray-300">Best Value</span>
                  </div>
                </div>
                <p className="text-sm text-gray-400 mt-4">
                  All purchases are backed by our money-back guarantee. Need help? Contact our 24/7 support team.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PackagesModal;