'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import toast from 'react-hot-toast';
import {
  UserRoundMinus,
  UserRoundPlus,
  Eye,
  EyeOff,
  MessageSquareShare,
  HandCoins,
  X,
  UserRoundX,
} from 'lucide-react';
import useUserActions from '@/hooks/useUserActions';
import HeartFillIcon from '@/assets/icons/Heart-Fill';
import HeartStrokeIcon from '@/assets/icons/Heart-Stroke';
import useUserInfoStore from '@/store/useUserInfoStore';
import useUserDetails from '@/hooks/useUserDetails';
import coinAC from '@/assets/images/stock-images/coin-ac.png';
import coinGC from '@/assets/images/stock-images/coin-gc.png';
import ProfileIcon from '@/assets/icons/Profile';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useAuthStore from '@/store/useAuthStore';
import {
  useCreateFriendsRequest,
  useUnFriendsRequest,
} from '@/reactQuery/chatWindowQuery';
import useModalStore from '@/store/useModalStore';
import { usePathname } from 'next/navigation';
import useGeneralStore from '@/store/useGeneralStore';
import profile_url from '@/assets/images/svg-images/profile-icon.svg';
import SCicon from '@/assets/icons/SCicon';
import GCicon from '@/assets/icons/GCicon';
import IconButton from '../Common/Button/IconButton';
import CustomImage from '../Common/CustomImage';
import Tooltip from '../Common/Tooltip';
import StoreModal from '../Store';
import ReactTooltip from '../Common/ReactTooltip';
import UserAvatar from '../DefaultImage';
import { useOpenChatWindow } from '@/utils/chat';
/* function CurrencySwitcher({ currency, setCurrency }) {
  return (
    <div className="flex gap-2 rounded-full bg-cetaceanBlue-1000 p-2">
      <button
        type="button"
        className={`flex items-center gap-2 rounded-full pr-2.5 ${currency === 'AC' ? 'bg-oxfordBlue-1000 text-white-1000' : 'text-steelTeal-1000'}`}
        onClick={() => setCurrency('AC')}
      >
        <Image
          src={coinAC}
          width={10000}
          height={10000}
          className="h-auto w-6 max-w-full xl:w-7"
          alt="Coin"
        />
        <span className="mt-0.5 inline-block text-sm leading-none tracking-wider">
          SC
        </span>
      </button>
    </div>
  );
} */

function CurrencySwitcher({ currency, setCurrency }) {
  const isGC = currency === 'GC';

  return (
    <div className="flex gap-2 rounded-full bg-cetaceanBlue-1000 p-2">
      <button
        type="button"
        className={`relative flex items-center gap-1  rounded-full ${
          isGC ? 'bg-oxfordBlue-1000  text-white-1000' : '  text-steelTeal-1000'
        }`}
        onClick={() => setCurrency(isGC ? 'SC' : 'GC')}
      >
        {/* {!isGC && <SCicon className=" h-6 w-6" />} */}
        <span
          className={`z-[2] flex h-7 w-7 items-center  justify-center rounded-full leading-none  ${isGC ? ' ' : ' bg-red-1000 font-semibold'}  text-sm tracking-wider text-white-1000`}
        >
          SC
        </span>
        <span
          className={`z-[2] flex h-7 w-7  items-center justify-center rounded-full leading-none  ${isGC ? ' bg-yellow-500 font-semibold ' : ''}  text-sm tracking-wider text-white-1000`}
        >
          GC
        </span>

        {/* {isGC && <GCicon className=" h-6 w-6" />} */}
      </button>
    </div>
  );
}

function StatsCard({ label, value }) {
  return (
    <div className="flex min-h-14 flex-col items-center justify-center gap-1.5 rounded-lg bg-cetaceanBlue-1000 p-2.5">
      <h5 className="text-white font-semi-bold mb-auto text-base leading-none">
        {value}
      </h5>
      <h6 className="mt-auto text-sm leading-none tracking-wider text-steelTeal-1000">
        {label}
      </h6>
    </div>
  );
}

function UserInfo({
  setUsername = () => {},
  setPrivateChatUserDetails = () => {},
}) {
  const pathname = usePathname();

  const checkIfGamePath = () => {
    const gamePathRegex = /^\/game\/[^/]+$/;

    if (gamePathRegex.test(pathname) || pathname.startsWith('/crash-game')) {
      toast.error('Can not access tip while playing game');
      return true;
    }
    return false;
  };

  const { openModal, closeModal } = useModalStore((state) => state);
  const { openChat: openChatFromStore, setOpenChat } = useGeneralStore(
    (state) => state,
  );
  const openChatWindow=useOpenChatWindow()
  //   const {
  //     coin:currency,
  //     setCoin:setCurrency,
  // } = useAuthStore((state) => state);
  const [currency, setCurrency] = useState('SC');
  const {
    isUserInfoModalOpen,
    selectedUserId,
    closeUserInfoModal,
    userDetails,
    loading,
    setUserDetails,
  } = useUserInfoStore();
  const { setIsPrivateChatOpen, setUserId } = usePrivateChatStore(
    (state) => state,
  );
  const { refetch, data: privateChatDetails } = useUserDetails();
  const userId = useAuthStore((state) => state?.userDetails?.userId);
  const { userDetails: loggedInUserDetails } = useAuthStore();
  const { ignoreUser, likeUser, unignoreUser, unlikeUser } = useUserActions({
    refetch,
  });

  const [isLiked, setIsLiked] = useState(false);
  const [isIgnored, setIsIgnored] = useState(false);
  const [imgError, setImgError] = useState(false);

  useEffect(() => {
    if (privateChatDetails) {
      setPrivateChatUserDetails(privateChatDetails);
    }
  }, [privateChatDetails]);
  const mutationRequest = useCreateFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      userDetails.friendRequestStatus = 'pending';
      setUserDetails(userDetails);
      refetch();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetch();
    },
  });

  const mutationUnFriendRequest = useUnFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      refetch();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetch();
    },
  });

  const unFriend = (unfriendUserId) => {
    mutationUnFriendRequest.mutate({ unfriendUserId });
  };

  useEffect(() => {
    if (userDetails) {
      setIsLiked(userDetails.liked);
      setIsIgnored(userDetails.ignored);
    }
  }, [userDetails]);

  const handleLike = () => {
    if (isLiked) {
      unlikeUser(selectedUserId);
    } else {
      likeUser(selectedUserId);
    }
    setIsLiked(!isLiked);
  };

  const handleIgnore = () => {
    if (isIgnored) {
      unignoreUser(selectedUserId);
    } else {
      ignoreUser(selectedUserId);
    }
    setIsIgnored(!isIgnored);
  };

  const openChat = () => {
    setUserId(userDetails?.userId);
    setIsPrivateChatOpen(true);
    closeUserInfoModal();
    closeModal();
    setUsername('');
    setOpenChat(true);
    openChatWindow()
  };

  const handleOpenTip = (userDetails) => {
    if (userDetails) {
      openModal(
        <StoreModal userDetails={userDetails} currentActiveTab="tips" />,
      );
    } else {
      toast.error('Wait to load user details!');
    }
  };

  const handleFriendRequest = () => {

    if (
      userDetails.playerPreferences &&
      userDetails.playerPreferences.REFUSE_FRIEND_REQUEST
    ) {
      toast.error('User is not accepting friend requests at the moment');
      return;
    }

    if (!loggedInUserDetails.phoneVerified) {
      toast.error('Please verify phone number to send a friend request');
      return;
    }
    mutationRequest.mutate({
      requesteeId: userDetails && +userDetails.userId,
    });
  };

  //   "playerPreferences": {
  //     "SOUND": true,
  //     "DO_NOT_TAG_CHAT_ROOM": true,
  //     "HIDE_PURCHASE_MESSAGE": true,
  //     "REFUSE_FRIEND_REQUEST": true
  // },
  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <ProfileIcon className="h-5 w-5 fill-white-1000" />
              <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                User Info
              </h3>
            </div>

            <IconButton
              onClick={() => closeModal()}
              className="h-6 w-6 min-w-6"
            >
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          {loading ? (
            <div className="text-white p-4">Loading...</div>
          ) : (
            <div className="p-4">
              <div className="mb-4 flex items-center justify-between gap-4 max-sm:flex-col">
                <div className="flex items-center gap-2 max-sm:gap-4">
                  <div className="relative">
                    {userDetails?.profileImage  && !imgError? (
                      <Image
                        src={userDetails?.profileImage}
                        alt="Profile"
                        width={76}
                        height={76}
                        className=" size-[60px] rounded-full max-sm:size-[4rem]"
                        loading='eager'
                        onError={()=>setImgError(true)}
                      />
                    ) : (
                      <UserAvatar
                        firstName={userDetails?.firstName}
                        lastName={userDetails?.lastName}
                        size={60}
                        className="size-[60px] rounded-full max-sm:size-[4rem]"
                      />
                    )}
                    <span className="absolute -left-1 bottom-[-5px] flex h-7 w-7 items-center justify-center rounded-full border-2 border-oxfordBlue-1000 bg-primary-1000 pt-1 text-center text-xs leading-none max-sm:-bottom-2 max-sm:-left-1">
                      {userDetails?.UserVipTiers[0]?.level}
                    </span>
                  </div>

                  <div className="flex flex-col gap-2">
                    <h5 className="text-white text-lg leading-none">
                      {userDetails?.username}
                    </h5>
                    <h6 className="rounded-full bg-cetaceanBlue-1000 px-2.5 py-1.5 text-sm leading-none text-steelTeal-1000">
                      Level: {userDetails?.UserVipTiers[0]?.level}
                    </h6>
                  </div>
                </div>

                <div className="flex items-center gap-3 max-sm:mt-5">
                  {userDetails?.userId && userDetails?.userId !== userId && (
                    <>
                      <ReactTooltip
                        message="Chat"
                        id="MessageSquareShareOpenChat"
                      />
                      <MessageSquareShare
                        id="MessageSquareShareOpenChat"
                        onClick={openChat}
                        className="cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                      />
                    </>
                  )}
                 
                  {userDetails?.userId && userDetails?.userId !== userId ? (
                    userDetails?.playerPreferences?.REFUSE_FRIEND_REQUEST ? (
                      <>
                        <ReactTooltip
                          message="Can Not Add-Friend"
                          id="UserRoundPlusFriend"
                        />
                        <UserRoundX
                          id="UserRoundPlusFriend"
                          onClick={() =>
                            handleFriendRequest(userDetails.userId)
                          }
                          className="cursor-not-allowed text-red-1000 hover:text-red-1000"
                        />
                      </>
                    ) : userDetails?.friendRequestStatus ? (
                      <>
                        <ReactTooltip
                          message="Un-Friend"
                          id="UserRoundMinusunFriend"
                        />
                        <UserRoundMinus
                          onClick={() => {
                            unFriend(userDetails && +userDetails.userId);
                          }}
                          className="cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                        />
                      </>
                    ) : (
                      <>
                        <ReactTooltip
                          message={
                            userDetails?.playerPreferences
                              ?.REFUSE_FRIEND_REQUEST
                              ? 'Can Not Add-Friend'
                              : 'Add-Friend'
                          }
                          id="UserRoundPlusFriend"
                          UserRoundX
                        />
                        <UserRoundPlus
                          id="UserRoundPlusFriend"
                          onClick={() =>
                            handleFriendRequest(userDetails?.userId)
                          }
                          className={`${
                            userDetails?.playerPreferences
                              ?.REFUSE_FRIEND_REQUEST
                              ? 'cursor-not-allowed'
                              : 'cursor-pointer'
                          } text-steelTeal-1000 hover:text-white-1000`}
                        />
                      </>
                    )
                  ) : null}
                  {userDetails?.userId && userDetails?.userId !== userId && (
                    <button
                      type="button"
                      onClick={handleIgnore}
                      className=""
                      disabled={loading}
                    >
                      {isIgnored ? (
                        <>
                          <ReactTooltip
                            message="Unignore-User"
                            id="EyeOffUnignore-User"
                          />
                          <EyeOff
                            id="EyeOffUnignore-User"
                            className="text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
                          />
                        </>
                      ) : (
                        <>
                          <ReactTooltip
                            message="Ignore-User"
                            id="EyeIgnore-User"
                          />
                          <Eye
                            id="EyeIgnore-User"
                            className="text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
                          />
                        </>
                      )}
                    </button>
                  )}

                  {userDetails?.userId && userDetails?.userId !== userId && (
                    <div className="flex items-center justify-center gap-3 rounded-full bg-cetaceanBlue-1000 p-2">
                      <button
                        onClick={handleLike}
                        type="button"
                        className=""
                        disabled={loading}
                      >
                        {isLiked ? (
                          <>
                            <ReactTooltip
                              message="Unlike"
                              id="HeartFillIconUnlike"
                            />
                            <HeartFillIcon
                              id="HeartFillIconUnlike"
                              className="h-5 w-5 fill-primary-1000 transition-all duration-300"
                            />
                          </>
                        ) : (
                          <>
                            <ReactTooltip
                              message="Like"
                              id="HeartStrokeIconLike"
                            />
                            <HeartStrokeIcon
                              id="HeartStrokeIconLike"
                              className="h-5 w-5 fill-primary-1000 transition-all duration-300"
                            />
                          </>
                        )}
                      </button>
                      <span className="mt-0.5 inline-block text-sm leading-none text-steelTeal-1000">
                        {userDetails?.likesCount}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              <div className="mb-4 flex items-center justify-end gap-4">
                <CurrencySwitcher
                  currency={currency}
                  setCurrency={setCurrency}
                />
              </div>

              <div className="grid grid-cols-2 gap-2 text-center">
                <StatsCard
                  label="WINS"
                  value={
                    currency === 'SC' ? userDetails?.SCWin : userDetails?.GCWin
                  }
                />
                <StatsCard
                  label="LOSSES"
                  value={
                    currency === 'SC'
                      ? userDetails?.SCLost
                      : userDetails?.GCLost
                  }
                />
                <StatsCard
                  label="BETS"
                  value={
                    currency === 'SC' ? userDetails?.SCBet : userDetails?.GCBet
                  }
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default UserInfo;
