'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import coinAC from '@/assets/images/stock-images/coin-ac.png';
import { FormInput } from '@/components/Form';
import { useTips } from '@/hooks/useTips';

const schema = yup.object().shape({
  username: yup.string().required('Username is required'),
  tipAmount: yup
    .number()
    .required('Tip amount is required')
    .positive('Tip amount must be positive')
    .typeError('Tip amount must be numbers only'),
});

function CurrencySwitcher({ currency, setCurrency }) {
  return (
    <div className="flex gap-2 rounded-full bg-cetaceanBlue-1000 p-2">
      <button
        type="button"
        className={`flex items-center gap-2 rounded-full bg-oxfordBlue-1000 pr-2.5 text-white-1000`}
        onClick={() => setCurrency('SC')}
      >
        <Image
          src={coinAC}
          width={10000}
          height={10000}
          className="h-auto w-6 max-w-full xl:w-7"
          alt="Coin"
        />
        <span className="mt-0.5 inline-block text-sm leading-none tracking-wider">
          SC
        </span>
      </button>
    </div>
  );
}

function Tips({ userDetails }) {
  console.log('userDetails', userDetails);
  const [currency, setCurrency] = useState('AC');
  const [isPublic, setIsPublic] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      username: userDetails?.username || '',
      tipAmount: '',
    },
  });

  const { sendTip, isLoading } = useTips();
  const onSubmit = (data) => {
    sendTip({
      receiverId: userDetails?.userId,
      receiverName: data.username,
      amount: data.tipAmount,
      amountType: 1,
      isTipPublic: isPublic,
    });
  };

  return (
    <div>
      <div className="mb-4 flex items-center justify-center">
        <CurrencySwitcher currency={currency} setCurrency={setCurrency} />
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <FormInput
          name="username"
          label="Username"
          control={control}
          placeholder="Username"
          error={errors.username}
          readOnly={userDetails ? true : false}
        />

        <FormInput
          name="tipAmount"
          label="Tip Amount"
          control={control}
          placeholder="Tip Amount"
          error={errors.tipAmount}
        />

        <div className="mb-4 flex items-center gap-2">
          <input
            type="checkbox"
            checked={isPublic}
            onChange={(e) => setIsPublic(e.target.checked)}
            id="publicTip"
            className="h-4 w-4"
          />
          <label htmlFor="publicTip" className="text-sm text-steelTeal-1000">
            Make tip public (will appear on global chat)
          </label>
        </div>

        <button
          type="submit"
          className="w-full rounded-lg bg-primary-1000 py-3 text-white-1000"
          disabled={isLoading}
        >
          Send {currency}
        </button>
      </form>
    </div>
  );
}

export default Tips;
