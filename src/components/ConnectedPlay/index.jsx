'use client';

// import Tabs from '@/components/Common/Tabs/ActivePlayerTab/index';
import CrossCircleIcon from '@/assets/icons/Cross-Circle';
import MessageGoldenIcon from '@/assets/icons/MessageGoldenIcon';
import useChatWindow from '@/hooks/useChatWindow';
import useGroupCall from '@/hooks/useGroupCall';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import useAuthStore from '@/store/useAuthStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import { useOpenChatWindow } from '@/utils/chat';
import { userStatusColor } from '@/utils/helper';
import useEmblaCarousel from 'embla-carousel-react';
import Image from 'next/image';
import ConnectedPlaySlider from '../ConnectedPlaySlider';
import UserAvatar from '../DefaultImage';
import ChatAvatar from '../ChatWindow/ChatAvatar';

const ConnectedPlay = () => {
  // const [activeTab, setActiveTab] = useState(0);
  const { handleDeclineCall } = useGroupCall();
  const { isAuthenticated } = useAuthStore();
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    dragFree: true,
    skipSnaps: false,
  });
  const openChatWindow = useOpenChatWindow();
  const { userDetails } = useAuthStore((state) => state);
  const { connectedPlay } = useActiveGroupStore((state) => state);
  const { setIsGroupChatOpen, setGroupId, setGroupChat, setGroupName } =
    useGroupChatStore((state) => state);
  const { setSection, section } = useChatWindow();
  const {
    setIsPrivateChatOpen,

    searchUserName,
    setSearchUserName,
  } = usePrivateChatStore((state) => state);
  console.log('🚀 ~ ConnectedPlay ~ connectedPlay:', connectedPlay);
  const openGroupChat = (groupId, groupName) => {
    // queryClient.invalidateQueries({ queryKey: ['GET_GROUP_CHATS_QUERY'] });
    setGroupId(groupId);
    setGroupName(groupName);
    setSection('GroupChat');
    setIsGroupChatOpen(true);
    setIsPrivateChatOpen(false);
    if (searchUserName != '') {
      if (!isAuthenticated) {
        return null;
      }
      setSearchUserName('');
    }
    openChatWindow();
  };

  if (!isAuthenticated || Object.keys(connectedPlay).length === 0) {
    return null;
  }
  return (
    <div className="relative mb-12  rounded-2xl py-6 shadow-lg">
      <div className="flex flex-col gap-6">
        <div className="embla w-full">
          <div className="flex flex-row items-start justify-between  gap-4 md:items-center">
            {/* Left section: Heading and Group Name */}
            <div className="flex flex-col gap-2 md:flex-row md:items-center">
              <h3 className="text-white text-2xl font-semibold">
                Connected Play
              </h3>
              <div className=" bg-white rounded-md py-2 text-[24px] font-medium text-gray-900 text-white-700 shadow-sm md:px-4">
                {connectedPlay?.groupName}
              </div>
            </div>

            {/* Right section: Icons */}
            <div className="flex items-center">
              <div className=" bg-white rounded-md shadow-md">
                <button
                  type="button"
                  aria-label="Open Messages"
                  className="px-2 py-2 text-center"
                  // onClick={() => setActiveTab(idx)}
                  onClick={() =>
                    openGroupChat(
                      connectedPlay?.groupId,
                      connectedPlay?.groupName,
                    )
                  }
                >
                  <MessageGoldenIcon />
                </button>
              </div>
              <div className=" bg-white rounded-md shadow-md">
                <button
                  type="button"
                  aria-label="Close"
                  className="px-2 py-2 text-center"
                  onClick={handleDeclineCall}
                >
                  <CrossCircleIcon fill="white" />
                </button>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center">
          <div
            className="embla__viewport scrollbar-none mr-[30px] overflow-auto "
            ref={emblaRef}
          >
            <div className="embla__container mt-4 flex gap-4">
              <div
                className="embla__slide flex flex-col items-center"
                key={'self'}
              >
                <div
                  className="relative h-[69px]"
                  style={{ width: 'max-content' }}
                >
                  <div
                    className="mx-auto inline-flex size-[60px] cursor-pointer items-center justify-center rounded-full border border-solid border-richBlack-900"
                    onClick={() => {
                      // openUserInfoModal(player?.relationUser?.userId);
                      // openModal(<UserInfo />);
                    }}
                  >
                    <ChatAvatar
                      profileImage={userDetails?.profileImage}
                      firstName={userDetails?.firstName}
                      lastName={userDetails?.lastName}
                      imageClassName="size-[60px] rounded-full"
                      imageWidth={60}
                      imageHeight={60}
                      avatarSize={60}
                    />
                    <span
                      className={`absolute bottom-3 right-1 z-[1] size-3 rounded-full ${userStatusColor(
                        userDetails?.currentStatus,
                      )}`}
                    />
                  </div>
                </div>
                <div className="text-center text-[0.75rem] font-normal text-white-1000">
                  {userDetails?.username}
                </div>
              </div>
              {connectedPlay?.otherUsersOnCall?.map(
                (player, i) =>
                  player?.userId !== userDetails?.userId && (
                    <div
                      className="embla__slide flex flex-col items-center"
                      key={i}
                    >
                      <div
                        className="relative h-[69px]"
                        style={{ width: 'max-content' }}
                      >
                        <div
                          className="mx-auto inline-flex size-[60px] cursor-pointer items-center justify-center rounded-full border border-solid border-richBlack-900"
                          onClick={() => {
                            // openUserInfoModal(player?.relationUser?.userId);
                            // openModal(<UserInfo />);
                          }}
                        >
                          {player?.profileImage ? (
                            <Image
                              src={player?.profileImage}
                              alt=""
                              width={1000}
                              height={1000}
                              className="size-[60px] rounded-full"
                              loading="eager"
                            />
                          ) : (
                            <UserAvatar
                              firstName={player?.firstName}
                              lastName={player?.lastName}
                              size={60}
                              className="size-[60px] rounded-full"
                            />
                          )}
                          <span
                            className={`absolute bottom-3 right-1 z-[1] size-3 rounded-full ${userStatusColor(
                              player?.currentStatus,
                            )}`}
                          />
                        </div>
                      </div>
                      <div className="text-center text-[0.75rem] font-normal text-white-1000">
                        {player?.username}
                      </div>
                    </div>
                  ),
              )}
            </div>
          </div>
        </div>
        <ConnectedPlaySlider />
      </div>
    </div>
  );
};

export default ConnectedPlay;
