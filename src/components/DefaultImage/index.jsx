import React from 'react';

function UserAvatar({
  firstName,
  lastName,
  size = 40,
  className,
  isLoading = false,
}) {
  const letterColors = {
    A: '#1a73e8', // Google Blue
    B: '#137333', // Google Green
    C: '#d93025', // Google Red
    D: '#f9ab00', // Google Yellow
    E: '#9aa0a6', // Google Gray
    F: '#9c27b0', // Purple
    G: '#ff6d01', // Orange
    H: '#00acc1', // Cyan
    I: '#5f6368', // Dark Gray
    J: '#7b1fa2', // Deep Purple
    K: '#e8710a', // Deep Orange
    L: '#0d7377', // Dark Teal
    M: '#c5221f', // Dark Red
    N: '#1565c0', // Dark Blue
    O: '#f57c00', // Amber
    P: '#ad1457', // Pink
    Q: '#6a4c93', // Purple Gray
    R: '#2e7d32', // Forest Green
    S: '#d84315', // Red Orange
    T: '#1976d2', // Blue
    U: '#388e3c', // Green
    V: '#7b1fa2', // Purple
    W: '#f57c00', // Orange
    X: '#5d4037', // Brown
    Y: '#303f9f', // Indigo
    Z: '#689f38', // Light Green
  };

  const defaultColor = '#EECE6C'; // Gold

  const getInitials = (first, last) => {
    const firstInitial =
      first && first.length > 0 ? first[0].toUpperCase() : '';
    const lastInitial = last && last.length > 0 ? last[0].toUpperCase() : '';
    return firstInitial + lastInitial;
  };

  const getBackgroundColor = (firstName) => {
    if (!firstName || firstName.length === 0) return defaultColor;
    const firstLetter = firstName[0].toUpperCase();
    return letterColors[firstLetter] || defaultColor;
  };

  const getTextColor = (bgColor) => {
    const lightColors = [
      '#F7DC6F',
      '#F8C471',
      '#F9E79F',
      '#FCF3CF',
      '#FDEAA7',
      '#FFD700',
    ];
    return lightColors.includes(bgColor) ? '#333333' : '#FFFFFF';
  };

  // Show loading state when data is not available or explicitly loading
  const showLoading = isLoading || (!firstName && !lastName);
  const initials = showLoading ? '' : getInitials(firstName, lastName);
  const backgroundColor = showLoading
    ? '#1a1a1a'
    : getBackgroundColor(firstName);
  const textColor = showLoading ? '#666666' : getTextColor(backgroundColor);

  const avatarStyle = {
    width: `${size}px`,
    height: `${size}px`,
    borderRadius: '50%',
    backgroundColor,
    color: textColor,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: `${size * 0.4}px`,
    fontWeight: 'bold',
    fontFamily: 'Arial, sans-serif',
    textTransform: 'uppercase',
    border: showLoading
      ? '2px solid #333333'
      : '2px solid rgba(255, 255, 255, 0.2)',
    boxShadow: showLoading
      ? '0 1px 3px rgba(0, 0, 0, 0.05)'
      : '0 2px 8px rgba(0, 0, 0, 0.1)',
    position: 'relative',
    overflow: 'hidden',
    transition: 'all 0.3s ease',
  };

  // Shimmer effect for loading
  const shimmerStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background:
      'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent)',
    animation: 'shimmer 1.5s infinite',
    borderRadius: '50%',
  };

  // Pulse dots loader
  const dotsContainerStyle = {
    display: 'flex',
    gap: '2px',
    alignItems: 'center',
    justifyContent: 'center',
  };

  const dotStyle = {
    width: `${Math.max(3, size * 0.08)}px`,
    height: `${Math.max(3, size * 0.08)}px`,
    borderRadius: '50%',
    backgroundColor: '#888888',
  };

  return (
    <>
      <style>
        {`
          @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }
          
          @keyframes pulse-dot-1 {
            0%, 60%, 100% { transform: scale(1); opacity: 0.4; }
            30% { transform: scale(1.2); opacity: 1; }
          }
          
          @keyframes pulse-dot-2 {
            0%, 60%, 100% { transform: scale(1); opacity: 0.4; }
            40% { transform: scale(1.2); opacity: 1; }
          }
          
          @keyframes pulse-dot-3 {
            0%, 60%, 100% { transform: scale(1); opacity: 0.4; }
            50% { transform: scale(1.2); opacity: 1; }
          }
          
          .pulse-dot-1 { animation: pulse-dot-1 1.4s infinite ease-in-out; }
          .pulse-dot-2 { animation: pulse-dot-2 1.4s infinite ease-in-out; }
          .pulse-dot-3 { animation: pulse-dot-3 1.4s infinite ease-in-out; }
        `}
      </style>

      <div style={avatarStyle} className={className}>
        {showLoading ? (
          <>
            <div style={shimmerStyle} />
            <div style={dotsContainerStyle}>
              <div style={dotStyle} className="pulse-dot-1" />
              <div style={dotStyle} className="pulse-dot-2" />
              <div style={dotStyle} className="pulse-dot-3" />
            </div>
          </>
        ) : (
          initials
        )}
      </div>
    </>
  );
}

export default UserAvatar;
