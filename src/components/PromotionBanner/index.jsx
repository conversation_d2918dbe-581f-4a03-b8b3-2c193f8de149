'use client';

import React, { useEffect, useState, useCallback } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import Image from 'next/image';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import useGeneralStore from '@/store/useGeneralStore';
import useAuthStore from '@/store/useAuthStore';
import useActiveGroupStore from '@/store/useActiveGroupStore';

function PromotionSlider() {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    slidesToScroll: 1,
    align: 'start',
  });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const { banners } = useGeneralStore((state) => state);
  const { connectedPlay } = useActiveGroupStore((state) => state);

  const { isAuthenticated } = useAuthStore((state) => state);
  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  // Autoplay logic
  useEffect(() => {
    if (!emblaApi) return;

    const autoplay = () => {
      if (emblaApi.canScrollNext()) {
        emblaApi.scrollNext();
      } else {
        emblaApi.scrollTo(0);
      }
    };

    const autoplayInterval = setInterval(autoplay, 4000); // Adjust the interval time as needed

    return () => {
      clearInterval(autoplayInterval);
    };
  }, [emblaApi]);
  if (Object.keys(connectedPlay).length != 0) {
    return null;
  }
  // console.log(isAuthenticated, "::::::::isAuthenticated", ":::::: !isAuthenticated",!isAuthenticated)
  return (
    <div className="embla mb-9 overflow-hidden" ref={emblaRef}>
      <div className="embla__container [&>.embla-slide]:bg-charcoalBlack-700 flex [&>.embla-slide]:relative [&>.embla-slide]:min-w-0 [&>.embla-slide]:flex-[0_0_100%] [&>.embla-slide]:overflow-hidden [&>.embla-slide]:rounded-2xl">
        {banners?.map((banner, index) => {
          // console.log(banner, "::::::::::banner")
          return (
            // Check visibility based on isAuthenticated status
            ((isAuthenticated &&
              (banner.visibility === 1 || banner.visibility === 2)) ||
              (!isAuthenticated &&
                (banner.visibility === 0 || banner.visibility === 2))) && (
              <div
                className="embla__slide embla-slide text-white"
                key={banner.pageBannerId}
              >
                <Image
                  src={banner.desktopImageUrl}
                  alt={`Banner ${index + 1}`}
                  className="rounded-sm"
                  width={1920} // Adjust as per your requirement
                  height={1080} // Adjust as per your requirement
                  layout="responsive"
                />
                {/* <div className="absolute left-[6%] top-0 max-w-[44%] w-full h-full flex flex-col justify-center">
                <h2 className="text-[calc(1vmin+2vw)] font-bold uppercase mt-2 gradient-text leading-none">
                    {banner.name || `Banner ${index + 1}`}
                </h2>
                {banner.btnText && (
                    <PrimaryButtonOutline className="max-w-36 w-full mt-7">
                        {banner.btnText}
                    </PrimaryButtonOutline>
                )}
            </div> */}
              </div>
            )
          );
        })}
      </div>
    </div>
  );
}

export default PromotionSlider;
