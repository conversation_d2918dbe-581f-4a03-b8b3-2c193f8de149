'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import VipShoppingCart from '@/assets/icons/Vip-ShoppingCart';
import { useVipTierRules } from '@/hooks/useVipTierRules';
import Skeleton from 'react-loading-skeleton';
import CrystalAndGemImage from '../../assets/images/CrystalsAndGems.png';
import IsolationGem from '../../assets/images/isolationGem.png';
import expired from '../../assets/images/vip-tier-level-expired.png';
import claimed from '../../assets/images/vip-tier-level-claimed.png';
import pending from '../../assets/images/vip-tier-level-pending.png';
import claimable from '../../assets/images/vip-tier-level-claimable.png';

// import expired1 from '../../assets/images/vip-tier-level1.png';
import VipSlider from './VipSlider';
import ReactTooltip from '../Common/ReactTooltip';
import { useClaimVipTier } from '@/reactQuery/generalQueries';
import toast from 'react-hot-toast';
import { useQueryClient } from '@tanstack/react-query';

function VipTier() {
  const queryClient = useQueryClient();
  //   const { isLoading,
  //     isError,
  //     isSuccess,
  //     vipTierRules,
  //     error, } = useVipTierRules()

  //   let completedSteps = 2420;
  //   const [milestones, setMilestones] = useState([])
  //   console.log(vipTierRules, ":::::::vipTierRules")

  //   const selectedVipTier = vipTierRules?.find(
  //     tier => tier.isUserActiveTier && tier.UserVipTiers.length > 0
  //   );
  // console.log(selectedVipTier, ":::::selectedVipTier");

  // // Get the wager amount, if it's available
  // const getWagerAmount = selectedVipTier?.wagerAmount;

  // console.log(getWagerAmount, "::::::::getWagerAmount")
  // if (getWagerAmount && !isNaN(getWagerAmount)) {
  //   // Calculate 20% of the wager amount
  //   const twentyPercent = getWagerAmount * 0.2;

  //   // Generate the 5 numbers by multiplying the 20% with different factors
  //   const generatedNumbers = [
  //       twentyPercent * 1,
  //       twentyPercent * 2,
  //       twentyPercent * 3,
  //       twentyPercent * 4,
  //       twentyPercent * 5
  //   ];

  //   // Replace the static milestones array with the generated numbers
  //   const updatedMilestones = milestones.map((milestone, index) => {
  //       return milestone + generatedNumbers[index]; // Add the generated value to each milestone
  //   });
  //   setMilestones(updatedMilestones);
  //   console.log(updatedMilestones);
  // } else {
  //   console.log("Invalid wager amount");
  // }
  const { isLoading, isError, isSuccess, vipTiers, userWagerAmount, error } =
    useVipTierRules();

  const [milestones, setMilestones] = useState([]);
  const [firstAndLast, setFirstAndLast] = useState({});

  console.log(
    vipTiers,
    ':::::::vipTiers      userWagerAmount:::::::::::::',
    userWagerAmount,
  );

  const selectedVipTier = vipTiers?.find(
    (tier) => tier.isUserActiveTier && tier.UserVipTiers.length > 0,
  );

  const currentIndex = vipTiers?.findIndex(
    (tier) => tier.isUserActiveTier && tier.UserVipTiers.length > 0,
  );

  const nextIndex = currentIndex + 1; // Get next index

  const nextVipTier = vipTiers?.[nextIndex]; // Get the next record safely
  console.log(nextVipTier, 'Next VIP Tier');
  const completedSteps = 3400;

  // get next tier wager amount

  const getnextVipTierWagerAmount = nextVipTier?.wagerAmount;
  console.log(
    '🚀 ~ VipTier ~ getnextVipTierWagerAmount:',
    getnextVipTierWagerAmount,
  );
  // Get the wager amount, if it's available
  const getWagerAmount = selectedVipTier?.wagerAmount;
  console.log(getWagerAmount, '::::::::getWagerAmount');

  useEffect(() => {
    if (getnextVipTierWagerAmount && !isNaN(getnextVipTierWagerAmount)) {
      // Calculate 20% of the wager amount
      const twentyPercent = getnextVipTierWagerAmount * 0.2;

      // Generate the 5 numbers by multiplying the 20% with different factors
      const generatedNumbers = [
        twentyPercent * 1,
        twentyPercent * 2,
        twentyPercent * 3,
        twentyPercent * 4,
        twentyPercent * 5,
      ];

      const firstNumber = generatedNumbers[0];
      const lastNumber = generatedNumbers[generatedNumbers.length - 1];
      setFirstAndLast({ first: firstNumber, last: lastNumber });
      // Replace the static milestones array with the generated numbers
      // const updatedMilestones = generatedNumbers.map((num, index) => {
      //   return num; // Assuming the static milestones start from 1350
      // });

      // setMilestones(updatedMilestones);
      // console.log(updatedMilestones);
    } else {
      console.log('Invalid wager amount');
    }
  }, [getnextVipTierWagerAmount, vipTiers]);

  const claimVIPTier = useClaimVipTier({
    onSuccess: (response) => {
      if (response?.data?.success) toast.success('Tier claimed successfully!');
      queryClient.invalidateQueries(['useGetVipTierRulesQuery']);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
      toast.error(message);
    },
  });
  const claimReward = (tierid) => {
    claimVIPTier.mutate({
      vipLevel: selectedVipTier?.level,
      subTierLevel: tierid,
    });
  };
  console.log(
    'selectedVipTier?.UserVipTiers?.[0]?.moreDetail',
    selectedVipTier,
    selectedVipTier?.UserVipTiers?.[0]?.moreDetails,
    selectedVipTier &&
      selectedVipTier?.UserVipTiers?.[0]?.moreDetails &&
      Object.keys(selectedVipTier?.UserVipTiers?.[0].moreDetails).length > 0,
  );
  const checkCurrentMileStone = () => {
    if (getnextVipTierWagerAmount && !isNaN(getnextVipTierWagerAmount)) {
      const twentyPercent = getnextVipTierWagerAmount * 0.2;

      // Generate the 5 numbers by multiplying the 20% with different factors
      const generatedNumbers = [
        twentyPercent * 1,
        twentyPercent * 2,
        twentyPercent * 3,
        twentyPercent * 4,
        twentyPercent * 5,
        twentyPercent * 6,

      ];
      console.log("🚀 ~ checkCurrentMileStone ~ generatedNumbers:", generatedNumbers)
      const mileStonePercentage = [2.2, 25, 48, 71, 94,100];

      let currentMileStone = 5;
      for (let i = 0; i < generatedNumbers.length; i++) {
        if (userWagerAmount <= generatedNumbers[i]) {
          currentMileStone = i + 1;
          break;
        }
      }
      console.log(
        '🚀 ~ checkCurrentMileStone ~ currentMileStone:',
        currentMileStone,
      );
      if (currentMileStone === 1) {
        return (
          (userWagerAmount / generatedNumbers[0]) *
          mileStonePercentage[currentMileStone - 1]
        );
      }
      if (currentMileStone > 1) {
        // return (
        //   mileStonePercentage[currentMileStone - 2] +
        //   ((generatedNumbers[0] -
        //     (generatedNumbers[currentMileStone - 1] - userWagerAmount)) /
        //     (generatedNumbers[currentMileStone - 1] -
        //       generatedNumbers[currentMileStone - 2])) *
        //     (mileStonePercentage[currentMileStone - 1] -
        //       mileStonePercentage[currentMileStone - 2])
        // );
        return (
          mileStonePercentage[currentMileStone - 2] +
          4.35 +
          ((generatedNumbers[0] -
            (generatedNumbers[currentMileStone - 1] - userWagerAmount)) /
            (generatedNumbers[currentMileStone - 1] -
              generatedNumbers[currentMileStone - 2])) *
            (mileStonePercentage[currentMileStone - 1] -
              mileStonePercentage[currentMileStone - 2] -
              4.35)
        );
      }
    }
  };
  function renderTierImage(value, key) {
    if (value) {
      const expireAt = value?.expireAt ? new Date(value.expireAt) : null;
      const now = new Date();
      if (
        value?.eligibleForClaim &&
        expireAt &&
        now.getTime() >= expireAt.getTime()
      ) {
        return (
          <>
            <ReactTooltip
              message={'Claim Expired'}
              id="claimexpired"
              position="bottom-start"
            />
            <span className="absolute -bottom-[20px] z-[1]" id="claimexpired">
              <Image
                src={expired}
                width="30px"
                height="30px"
                alt=""
                className="h-[30px] w-[30px]"
              />
            </span>
          </>
        );
      }
      if (
        value?.eligibleForClaim &&
        expireAt &&
        now.getTime() < expireAt.getTime()
      ) {
        if (claimVIPTier?.isPending) {
          return (
            <span className="absolute -bottom-[15px] z-[1]">
              <svg
                className="text-white mr-2 h-5 w-5 animate-spin"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path fill="currentColor" d="M4 12a8 8 0 018-8v8H4z" />
              </svg>
            </span>
          );
        } else
          return (
            <>
              <ReactTooltip
                message={'Claim Reward'}
                id="claimReward"
                position="bottom-start"
              />
              <span
                className="absolute -bottom-[20px] z-[1] animate-bounce cursor-pointer"
                id="claimReward"
                onClick={() => claimReward(key)}
              >
                <Image
                  src={claimable}
                  width="30px"
                  height="30px"
                  alt=""
                  className="h-[30px] w-[30px]"
                />
              </span>
            </>
          );
      }
      if (!value?.eligibleForClaim) {
        return (
          <>
            {' '}
            <ReactTooltip
              message={'Reward Claimed'}
              id="rewardClaimed"
              position="bottom-start"
            />
            <span className="absolute -bottom-[20px] z-[1]" id="rewardClaimed">
              <Image
                src={claimed}
                width="30px"
                height="30px"
                alt=""
                className="h-[30px] w-[30px]"
              />
            </span>
          </>
        );
      }
    } else {
      return (
        <span className="absolute -bottom-[20px] z-[1]">
          <Image
            src={pending}
            width="30px"
            height="30px"
            alt=""
            className="h-[30px] w-[30px]"
          />
        </span>
      );
    }
  }
  return (
    <div>
      <div className="flex items-baseline gap-4">
        <VipShoppingCart />
        <h4 className="text-2xl font-normal">
          {' '}
          VIP Tier
          {/* {selectedVipTier?.name} */}
        </h4>
      </div>
      <div className="flex h-dvh max-h-[812px] gap-12 max-md:flex-wrap max-sm:gap-1">
        <div className="flex h-full w-full max-w-[800px] flex-col justify-center gap-[100px] max-md:gap-14 max-sm:mt-8 max-sm:h-fit max-sm:justify-start max-sm:gap-5">
          <div className="relative flex justify-center ">
            <div className="flex aspect-[681/367] h-full max-h-[367px] w-full max-w-[681px] items-center justify-center bg-vip-tier bg-cover bg-no-repeat ">
              <div className="flex  h-full w-full items-center justify-center gap-8 px-11 py-9 max-sm:gap-4">
                <div>
                  <Image
                    src={CrystalAndGemImage}
                    width="204px"
                    height="204px"
                    alt=""
                  />
                </div>

                <div className="flex max-w-[282px] flex-col">
                  {isLoading ? (
                    <Skeleton width={250} height={120} className="block" />
                  ) : (
                    <>
                      <span className="text-4xl font-black leading-8 text-primary-900 max-lg:text-2xl max-lg:leading-5 max-sm:text-xs">
                        {selectedVipTier?.name}
                      </span>
                      <span className="text-4xl font-light leading-8 text-white-1000 max-lg:text-2xl max-lg:leading-5 max-sm:text-xs">
                        {' '}
                        is your current tier
                      </span>
                      <span className="text-steelTeal-500 py-4 text-[13px] font-normal leading-3 max-md:py-2 max-sm:py-1">
                        your progress is an accumulated sum through your wager,
                        increase through tiers to earn bigger rewards
                      </span>
                      <span className="text-xl font-bold  leading-[18px] max-lg:text-base  max-lg:leading-4 max-sm:text-xs">
                        {userWagerAmount}{' '}
                        <span className="text-richBlack-700">
                          /{getnextVipTierWagerAmount}
                        </span>
                      </span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
          {nextIndex < vipTiers?.length && (
            <div className="mx-auto my-8 w-full max-w-2xl">
              <div className="flex justify-between ">
                {vipTiers?.[nextIndex]?.moreDetails &&
                  Object?.entries(vipTiers?.[nextIndex]?.moreDetails).map(
                    (milestone, index) => (
                      <div
                        key={index}
                        className="relative flex flex-col items-center"
                      >
                        <div className="mb-6 flex flex-col items-center gap-1">
                          <Image
                            src={IsolationGem}
                            width="200px"
                            height="200px"
                            alt=""
                            className={
                              userWagerAmount >= milestone[1] ? '' : 'grayscale'
                            }
                          />
                          <span
                            className={`text-base font-bold ${userWagerAmount >= milestone[1] ? 'text-white-1000' : 'text-richBlack-900'}`}
                          >
                            {milestone[1]}
                          </span>
                        </div>
                        {renderTierImage(
                          selectedVipTier?.UserVipTiers?.[0]?.moreDetails?.[
                            milestone[0]
                          ],
                          milestone[0],
                        )}
                      </div>
                    ),
                  )}
              </div>
              <ReactTooltip
                message={userWagerAmount}
                id="my-completed-steps"
                position="bottom-start"
              />
              <div
                id="my-completed-steps"
                className="relative h-2 rounded-full bg-richBlack-600"
              >
                <div
                  className="absolute h-2 rounded-full bg-progressBg"
                  style={{
                    // width: `${((userWagerAmount - firstAndLast?.first) / (firstAndLast.last - firstAndLast?.first)) * 100}%`,
                    width: `${ Math.min( checkCurrentMileStone(),100)}%`,
                  }}
                />
              </div>
              {/* <div className="mt-[24px] flex w-full items-center justify-center">
                {(() => {
                  const vipTier = selectedVipTier?.UserVipTiers?.[0];
                  const moreDetails = vipTier?.moreDetails;
                  console.log("🚀 ~ VipTier ~ moreDetails:", moreDetails)

                  // ✅ Check if `moreDetails` is missing or empty BEFORE proceeding
                  if (!moreDetails || typeof moreDetails !== 'object' || Object.keys(moreDetails).length === 0) {
                    console.log("Skipping as moreDetails is missing or empty", moreDetails);
                    return null; // Exit early if `moreDetails` is missing or empty
                  }

                  console.log("moreDetails exists", moreDetails);

                  let showButton = null; // Store the button to display

                  for (const [key, value] of Object.entries(moreDetails)) {
                    if (!value) continue;

                    const expireAt = value?.expireAt ? new Date(value.expireAt) : null;
                    const now = new Date();

                    console.log('🚀 ~ Checking Tier:', key, value);

                    // Skip expired rewards
                    if (value?.eligibleForClaim && expireAt && now >= expireAt) continue;

                    // If eligible and not expired, show "Claim Rewards" and STOP checking further
                    if (value?.eligibleForClaim && expireAt && now < expireAt) {
                      showButton = (
                        <button
                          className={`rounded-md px-6 py-2 text-center text-xl font-normal 
                      ${claimVIPTier?.isPending ? 'cursor-not-allowed bg-gray-500' : 'bg-primary-900'}`}
                          onClick={() => claimReward(key)}
                          disabled={claimVIPTier?.isPending}
                        >
                          {claimVIPTier?.isPending ? (
                            <span className="flex items-center">
                              <svg
                                className="text-white mr-2 h-5 w-5 animate-spin"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <circle
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  strokeWidth="4"
                                />
                                <path
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8v8H4z"
                                />
                              </svg>
                              Claiming...
                            </span>
                          ) : (
                            'Claim Rewards'
                          )}
                        </button>
                      );
                      break; // Stop loop after finding first valid button
                    }

                    // If no valid claim found yet, store "Claimed" but don't stop immediately
                    if (!value?.eligibleForClaim) {
                      showButton = (
                        <button className="cursor-default rounded-md bg-primary-900 px-6 py-2 text-center text-xl font-normal">
                          Claimed
                        </button>
                      );
                    }
                  }

                  return showButton;
                })()}

              </div> */}
            </div>
          )}
        </div>
        <div className="">
          <VipSlider vipTierRules={vipTiers} isLoading={isLoading} />
        </div>
      </div>
    </div>
  );
}

export default VipTier;
