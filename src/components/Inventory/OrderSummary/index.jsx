'use client';

import React, { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import EditPencilIcon from '@/assets/icons/Edit-Pencil';
import MinusIcon from '@/assets/icons/MinusIcon';
import PlusIcon from '@/assets/icons/PlusIcon';
import Image from 'next/image';
import useCart from '@/hooks/useCartHook';
import { useCityQuery, useStateQuery } from '@/reactQuery/authQuery';
import {
  useConfirmOrderMutation,
  useUpdateCartMutation,
} from '@/reactQuery/inventoryQuery';
import { useRouter } from 'next/navigation';

function OrderSummary({ onNext, onPrevious, userMeta }) {
  const router = useRouter();
  const { mutate: updateCart, isLoading: isUpdating } = useUpdateCartMutation();

  const { mutate: confirmOrder, ...others } = useConfirmOrderMutation({
    onNext,
  });
  console.log('others...', others);
  const [orderSessionId, setOrderSessionId] = useState(null);
  const { refetch, cartItems, cartLoading } = useCart();
  const [address, setAddress] = useState({
    deliveryAddress1: '',
    deliveryAddress2: '',
    city: '',
    state: '',
    postalCode: '',
  });
  const [deliveryCharge, setDeliveryCharge] = useState(null);

  const { data: stateData, isLoading: stateLoading } = useStateQuery({
    countryCode: 'MM',
  });
  const { data: cityData, isLoading: cityLoading } = useCityQuery({
    countryCode: 'MM',
  });

  const cityOptions = () => {
    return cityLoading
      ? []
      : cityData?.map((item) => ({
          value: item?.inventoryDeliveryChargeId,
          label: item?.city,
        }));
  };

  const stateOptions = () => {
    return stateLoading
      ? []
      : stateData?.map((item) => ({
          value: item?.state_id,
          label: item?.name,
        }));
  };

  useEffect(() => {
    const defaultAddress = userMeta?.defaultDeliveryAddress;

    if (!cartLoading && cartItems.rows.length > 0) {
      const { orderSessionId, deliveryCharges } = cartItems?.rows[0];
      setOrderSessionId(orderSessionId);
      setDeliveryCharge(deliveryCharges);
    } else {
      setOrderSessionId(null);
      setDeliveryCharge(null);
    }

    if (defaultAddress) {
      setAddress({
        deliveryAddress1: defaultAddress.deliveryAddress1,
        deliveryAddress2: defaultAddress.deliveryAddress2,
        city: cityOptions().find((item) => item.value === defaultAddress.city),
        state: stateOptions().find(
          (item) => item.value === defaultAddress.state,
        ),
        postalCode: defaultAddress.postalCode,
      });
    } else {
      const {
        delivery_address_1,
        delivery_address_2,
        city,
        state,
        postalCode,
      } = cartItems.rows[0];

      setAddress({
        deliveryAddress1: delivery_address_1,
        deliveryAddress2: delivery_address_2,
        city: cityOptions().find((item) => item.value === city),
        state: stateOptions().find((item) => item.value === state),
        postalCode,
      });
    }
  }, [cartItems, userMeta, cartLoading]);

  const handleQuantityChange = (inventoryId, action) => {
    updateCart({
      inventoryId,
      orderSessionId,
      action,
    });
  };

  useEffect(() => {
    if (cartItems?.rows?.length === 0) {
      toast.error('Your cart is empty. Redirecting to inventory.');
      router.push('/inventory');
    }
  }, [cartItems]);

  const calculateSubtotal = () => {
    return (
      (cartItems?.rows?.length > 0 &&
        cartItems?.rows?.reduce((acc, cur) => {
          const price = parseFloat(cur.price);
          const quantity = parseInt(cur.quantity, 10);
          return acc + price * quantity;
        }, 0)) ||
      0
    );
  };

  const calculateTotal = () => {
    return calculateSubtotal() + Number(deliveryCharge);
  };

  const handleConfirmOrder = () => {
    const hasInactiveItems = cartItems?.rows?.some(
      (item) => item.inventory?.isActive === false,
    );

    if (hasInactiveItems) {
      toast.error(
        'One or more items in your cart are inactive. Please remove them before confirming the order.',
      );
      return;
    }

    confirmOrder({ orderSessionId });
  };

  return (
    <div className="m-[0_auto] mt-7 w-full max-w-[864px]">
      <div className="flex items-center justify-between rounded-md  bg-tiber-1000 px-4 py-10">
        <p>
          {address?.deliveryAddress1 && address.deliveryAddress1}
          {address?.deliveryAddress2 && `, ${address.deliveryAddress2}`}
          {address?.city?.label && `, ${address.city.label}`}
          {address?.state?.label && `, ${address.state.label}`}
          {address?.postalCode && `, ${address.postalCode}`}
        </p>
        <button
          type="button"
          onClick={onPrevious}
          className="flex h-9 w-9 items-center justify-center rounded-md border border-solid border-steelTeal-1000"
        >
          <EditPencilIcon className="fill-steelTeal-1000" />
        </button>
      </div>

      <div className="mt-7 grid gap-8">
        {cartItems?.rows?.map(({ inventoryId, quantity, inventory, price }) => (
          <div className="flex items-start justify-between" key={inventoryId}>
            <div className="flex items-start gap-4">
              <Image
                src={inventory?.inventoryImages[0]}
                width={10000}
                height={10000}
                className="h-24 w-24 rounded-lg object-contain max-md:h-12 max-md:w-12"
                alt="product image"
              />

              <div>
                <h6 className="text-base font-bold text-white-1000 max-xs:text-sm">
                  {inventory?.name}
                </h6>
                <p className="text-base font-normal text-white-1000 max-xs:text-sm">
                  {price} Tickets
                </p>
                <p className="mt-2 text-red-1000">
                  {inventory?.isActive == false
                    ? 'This item is currently in-active'
                    : null}
                </p>
              </div>
            </div>
            <div className="flex flex-col items-end justify-between gap-4">
              <p className="text-xl font-bold text-white-1000 max-xs:text-base">
                {price * quantity} SC
              </p>
              <div className="flex items-center rounded-lg border border-solid border-steelTeal-1000">
                <a
                  className="h-full cursor-pointer px-2.5 py-2"
                  onClick={() => handleQuantityChange(inventoryId, 'deduct')}
                >
                  <MinusIcon />
                </a>
                <span className="h-full border-x border-solid border-steelTeal-1000 px-2.5 py-1 text-base leading-tight max-xs:text-sm">
                  {quantity}
                </span>
                <a
                  className="h-full cursor-pointer px-2.5 py-2"
                  onClick={() => handleQuantityChange(inventoryId, 'add')}
                >
                  <PlusIcon />
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-7  rounded-md bg-tiber-1000 p-4">
        <div className="w flex items-center justify-between">
          <p className="text-base font-normal capitalize text-steelTeal-1000">
            Subtotal
          </p>
          <p>
            {calculateSubtotal()}
            <span> AC</span>
          </p>
        </div>
        <div className="w flex items-center justify-between">
          <p className="text-base font-normal capitalize text-steelTeal-1000">
            Shipping
          </p>
          <p>
            {deliveryCharge}
            <span> AC</span>
          </p>
        </div>

        <div className="mt-5 flex items-center justify-between border-t border-solid border-steelTeal-1000 bg-maastrichtBlue-1000 px-2.5 py-4">
          <p className="text-lg text-white-1000">Total</p>

          <p className="text-white text-3xl font-normal max-xs:text-xl">
            {calculateTotal()}
            <span> AC</span>
          </p>
        </div>
      </div>

      <div className="mt-7 text-center">
        <PrimaryButton className="capitalize" onClick={handleConfirmOrder}>
          confirm order
        </PrimaryButton>
      </div>
    </div>
  );
}

export default OrderSummary;
