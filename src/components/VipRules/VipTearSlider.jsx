'use client';

import React from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import Image from 'next/image';
import LevelImg1 from '../../assets/images/svg-images/level1.svg';
import DoubleCheckIcon from '@/assets/icons/DoubleCheckIcon';
import DoubleMinus from '@/assets/icons/DoubleMinus';
import LevelFrame from '@/assets/icons/LevelFrame';
import ActiveLevelArrow from '@/assets/icons/ActiveLevelArrow';
import { useVipTierRules } from '@/hooks/useVipTierRules';
import useAuthStore from '@/store/useAuthStore';

function VipTearSlider({}) {
  const [emblaRef] = useEmblaCarousel();
  const { vipTierRules } = useVipTierRules();

  const { userDetails } = useAuthStore((state) => state);

  console.log('vipTierRules  12361273678126378162783686 ', vipTierRules?.vipTiers);
  return (
    <div className="embla overflow-hidden" ref={emblaRef}>
      <div className="embla__container flex [&>.emblaSlide]:mx-[1%] [&>.emblaSlide]:min-w-0 [&>.emblaSlide]:flex-[0_0_18%] [&>.emblaSlide]:max-md:flex-[0_0_31%] ">
        {vipTierRules?.vipTiers?.map(
          ({
            cashback,
            dailyBonus,
            levelUpBonus,
            loginBonus,
            monthlyBonus,
            rakeback,
            wagerAmount,
            weeklyBonus,
            level,
          }) => (
            <div className="embla__slide emblaSlide">
              <div className="flex flex-col gap-4">
                <div className="relative flex items-center justify-center drop-shadow-vipCoinShadow">
                  <Image
                    src={LevelImg1}
                    width={10000}
                    height={10000}
                    className="h-28 w-full max-w-28 object-contain"
                    alt="product image"
                  />
                  <div className="absolute top-[62%] -translate-y-1/2 text-2xl font-extrabold max-sm:text-xl ">
                    {level}
                  </div>
                  {userDetails?.level > level ? (
                    <div className="absolute -right-[19%] ">
                      <ActiveLevelArrow className="w-full max-w-[36px] max-xxl:max-w-[20px] max-md:hidden" />
                    </div>
                  ) : null}
                </div>

                <div
                  className={`relative flex flex-col gap-2 rounded-b-[20px]  bg-darkBlue-800 ${userDetails?.level > level ? 'border border-solid border-scarlet-900 shadow-vipLevelShadow' : ''}`}
                >
                  <div className="absolute -top-1.5">
                    {userDetails?.level > level ? (
                      <LevelFrame className="scale-z-[1.5] h-auto w-full scale-x-[1.15] scale-y-[1.15]" />
                    ) : null}
                  </div>
                  <div className="flex h-12 items-center justify-center text-base font-bold max-lg:text-sm desktop:text-sm uppertab:text-sm">
                    {wagerAmount} <span>SC</span>
                  </div>
                  <div className="flex h-12 items-center justify-center max-lg:text-sm desktop:text-sm uppertab:text-sm">
                    {loginBonus ? <DoubleCheckIcon /> : <DoubleMinus />}
                  </div>
                  <div className="flex h-12 items-center justify-center max-lg:text-sm desktop:text-sm uppertab:text-sm">
                    {dailyBonus ? <DoubleCheckIcon /> : <DoubleMinus />}
                  </div>
                  <div className="flex h-12 items-center justify-center max-lg:text-sm desktop:text-sm uppertab:text-sm">
                    {weeklyBonus ? <DoubleCheckIcon /> : <DoubleMinus />}
                  </div>
                  <div className="flex h-12 items-center justify-center max-lg:text-sm desktop:text-sm uppertab:text-sm">
                    {monthlyBonus ? <DoubleCheckIcon /> : <DoubleMinus />}
                  </div>
                  <div className="flex h-12 items-center justify-center max-lg:text-sm desktop:text-sm uppertab:text-sm">
                    {rakeback}
                  </div>
                  <div className="flex h-12 items-center justify-center max-lg:text-sm desktop:text-sm uppertab:text-sm">
                    {cashback ? <DoubleCheckIcon /> : <DoubleMinus />}
                  </div>
                  <div className="flex h-12 items-center justify-center max-lg:text-sm desktop:text-sm uppertab:text-sm">
                    {levelUpBonus ? <DoubleCheckIcon /> : <DoubleMinus />}
                  </div>
                </div>
              </div>
            </div>
          ),
        )}
      </div>
    </div>
  );
}

export default VipTearSlider;
