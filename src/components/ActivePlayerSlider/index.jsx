'use client';

import ArrowCircleDownIcon from '@/assets/icons/Arrow-Circle-Down';
import ArrowCircleLeftIcon from '@/assets/icons/Arrow-Circle-Left';
import CallGoldenIcon from '@/assets/icons/CallGoldenIcon';
import CallIcon from '@/assets/icons/CallIcon';
import ChatIcon from '@/assets/icons/Chat';
import ChatAvatar from '@/components/ChatWindow/ChatAvatar';
import useActivePlayers from '@/hooks/useActivePlayer';
import usePrivateCall from '@/hooks/usePrivateCall';
import { useCreateFriendsRequest } from '@/reactQuery/chatWindowQuery';
import useActivePlayerStore from '@/store/useActivePlayeStore';
import useAuthStore from '@/store/useAuthStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import { useOpenChatWindow } from '@/utils/chat';
import { userStatusColor } from '@/utils/helper';
import useEmblaCarousel from 'embla-carousel-react';
import { Clock, UserRound, UserRoundPlus } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import 'swiper/css';
import 'swiper/css/navigation';
import { Mousewheel } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import GameCard from '../Common/GameCard';
import UserInfo from '../UserInfoModal';
import ActivePlayerModel from './ActivePlayerModal';

const cardAnimation = {
  initial: {
    opacity: 0,
    x: '-100vw',
    y: '100%',
    scale: 0,
  },
  animate: {
    opacity: 1,
    x: 0,
    y: 0,
    scale: 1,
  },
  transition: (index) => ({
    duration: 0.5,
    ease: 'easeOut',
    delay: index * 0.1,
  }),
};

function ActivePlayerSlider({
  className = '',
  gamesList,
  id,
  isAuthenticated,
  tab,
  ...props
}) {
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const swiperRef = useRef(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    slidesToScroll: 2,
    align: 'start',
    dragFree: false,
  });

  const { myfriends, publicUsers, myFriendsLoading, activeTab } =
    useActivePlayerStore((state) => state);
  const { refetchPublicUsers } = useActivePlayers();
  const {
    setIsPrivateChatOpen,
    setUserId,
    isCallActive,
    userId,
    searchUserName,
    setSearchUserName,
  } = usePrivateChatStore((state) => state);
  const openChatWindow = useOpenChatWindow();
  const { initiateCall, disconnectCall } = usePrivateCall();
  const { openModal } = useModalStore();
  const { openUserInfoModal } = useUserInfoStore((state) => state);
  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    // setSelectedIndex(emblaApi.selectedScrollSnap());
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  console.log(props, 'sldklsdksldksldsl');
  console.log(
    'myFriendsLoading',
    myFriendsLoading,
    myfriends?.length == 0,
    !myFriendsLoading,
  );

  const mutationRequest = useCreateFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);

      refetchPublicUsers();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetchPublicUsers();
    },
  });

  const sendFriendRequest = (id) => {
    mutationRequest.mutate({
      requesteeId: +id,
    });
  };

  const openChat = (userId) => {
    setUserId(userId);
    setIsPrivateChatOpen(true);
    if (searchUserName != '') {
      setSearchUserName('');
    }
    openChatWindow();
  };
  const { userDetails } = useAuthStore((state) => state);
  console.log('🚀 ~ userDetails:', userDetails);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const totalGames = activeTab === 0 ? myfriends?.length : publicUsers.length;
  const noNavigationNeeded = isMobile ? totalGames <= 2 : totalGames <= 5;

  const handleSlidePrev = () => {
    if (swiperRef.current) {
      swiperRef.current.slideTo(
        Math.max(swiperRef.current.activeIndex - (isMobile ? 2 : 5), 0),
      );
    }
  };

  const handleSlideNext = () => {
    if (swiperRef.current) {
      swiperRef.current.slideTo(
        swiperRef.current.activeIndex + (isMobile ? 2 : 5),
      );
    }
  };

  return (
    <div className="max-w-containerWidth mx-auto w-full">
      {((activeTab === 0 && myfriends?.length != 0) ||
        (activeTab == 1 && publicUsers?.length > 0)) && (
        <div className="absolute right-0 top-7 flex flex-row items-center gap-2 ">
          <div className="flex gap-2">
            <button
              onClick={handleSlidePrev}
              disabled={isBeginning || noNavigationNeeded}
            >
              <ArrowCircleLeftIcon
                className={`size-7 fill-steelTeal-1000 transition-opacity ${
                  isBeginning || noNavigationNeeded
                    ? 'cursor-not-allowed opacity-30'
                    : 'cursor-pointer'
                }`}
              />
            </button>

            <button
              onClick={handleSlideNext}
              disabled={isEnd || noNavigationNeeded}
            >
              <ArrowCircleDownIcon
                className={`size-7 -rotate-90 fill-steelTeal-1000 transition-opacity ${
                  isEnd || noNavigationNeeded
                    ? 'cursor-not-allowed opacity-30'
                    : 'cursor-pointer'
                }`}
              />
            </button>
          </div>
        </div>
      )}
      <div className={`${className}`}>
        <div className="embla m-auto">
          <div>
            <Swiper
              breakpoints={{
                0: {
                  slidesPerView: 2,
                  slidesPerGroup: 2,
                  spaceBetween: 7,
                },
                500: {
                  slidesPerView: 3,
                  slidesPerGroup: 3,
                  spaceBetween: 14,
                },
                768: {
                  slidesPerView: 4,
                  slidesPerGroup: 4,
                  spaceBetween: 14,
                },
                1024: {
                  slidesPerView: 5,
                  slidesPerGroup: 5,
                  spaceBetween: 14,
                },
              }}
              modules={[Mousewheel]}
              mousewheel={{ forceToAxis: true }}
              onSwiper={(swiper) => {
                swiperRef.current = swiper;
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              onSlideChange={(swiper) => {
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              className="!px-1"
            >
              {/* <div className=""> */}
              {(activeTab === 0 ? myfriends : publicUsers)?.map((user) => (
                <SwiperSlide
                  key={user?.id}
                  style={{ overflow: 'visible', padding: '10px 0' }}
                >
                  <div className="">
                    <GameCard
                      src={user?.thumbnail}
                      width={200}
                      height={200}
                      gameId={user?.gameId}
                      isFavorite={user?.isFavorite}
                      alt={user?.gameName}
                    />
                    <div className="py-2">
                      {user?.players?.map(
                        (player, index) =>
                          index < 3 && (
                            <div
                              className="inline-flex w-full max-w-[181px] items-center  justify-between rounded-full border border-solid border-secondaryBorder px-2 py-1"
                              key={index}
                            >
                              <div className="inline-flex items-center gap-2 ">
                                <div className="relative inline-flex size-[30px] shrink-0 cursor-pointer">
                                  <div className="pointer-events-none absolute inset-0 z-10 size-[30px] rounded-full border border-solid border-richBlack-900"></div>
                                  <div
                                    className="size-[30px] overflow-hidden rounded-full"
                                    onClick={() => {
                                      openUserInfoModal(player?.userId);
                                      openModal(<UserInfo />);
                                    }}
                                  >
                                    <ChatAvatar
                                      profileImage={player?.profileImage}
                                      firstName={player?.firstName}
                                      lastName={player?.lastName}
                                      imageClassName="h-full w-full rounded-full object-cover object-center"
                                      imageWidth={30}
                                      imageHeight={30}
                                      avatarSize={30}
                                    />
                                  </div>

                                  <span
                                    className={`absolute bottom-0  right-0 z-20 size-2 rounded-full ${userStatusColor(player?.currentStatus || 'AVAILABLE')}`}
                                  ></span>
                                </div>
                                <h4 className="text-white max-w-[85px] truncate text-base font-normal">
                                  {player?.username}
                                </h4>
                              </div>
                              {player?.areFriends ? (
                                <div className="flex items-center gap-1">
                                  {(player?.currentStatus == 'AVAILABLE' ||
                                    player?.currentStatus == null ||
                                    player?.currentStatus == 'BUSY') &&
                                    (isCallActive ? (
                                      userId == player?.userId && (
                                        <CallIcon
                                          className={` ${isCallActive ? 'fill-red-600' : 'text-blue-400'} size-4.1 cursor-pointer`}
                                          onClick={() => {
                                            if (isCallActive) {
                                              disconnectCall(player?.userId);
                                            } else {
                                              initiateCall(player?.userId);
                                            }
                                          }}
                                        />
                                      )
                                    ) : (
                                      <CallGoldenIcon
                                        className={` ${isCallActive ? 'fill-red-600' : 'text-blue-400'} size-4.1 cursor-pointer`}
                                        onClick={() => {
                                          if (isCallActive) {
                                            disconnectCall(player?.userId);
                                          } else {
                                            initiateCall(player?.userId);
                                          }
                                        }}
                                      />
                                    ))}
                                  <ChatIcon
                                    className="size-4.1 h-[20px] w-[20px] cursor-pointer fill-red-400"
                                    onClick={() => openChat(player?.userId)}
                                  />
                                </div>
                              ) : (
                                <div className="flex items-center gap-1">
                                  {player?.userId != userDetails?.userId &&
                                    !player?.areFriends && (
                                      <>
                                        {player?.friendRequestStatus ==
                                          false && (
                                          <UserRoundPlus
                                            id="UserRoundPlusFriend"
                                            onClick={() =>
                                              sendFriendRequest(player?.userId)
                                            }
                                            className="h-[20px] w-[20px] cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                                          />
                                        )}
                                        {player?.friendRequestStatus ==
                                          'pending' && (
                                          <div
                                            style={{
                                              position: 'relative',
                                              display: 'inline-block',
                                            }}
                                          >
                                            <UserRound size={20} />
                                            <Clock
                                              size={12}
                                              style={{
                                                position: 'absolute',
                                                bottom: '-4px',
                                                right: '-2px',
                                                background: 'black',
                                                borderRadius: '50%',
                                              }}
                                            />
                                          </div>
                                        )}
                                      </>
                                    )}
                                </div>
                              )}
                            </div>
                          ),
                      )}
                    </div>
                    {user?.players?.length > 3 && (
                      <PrimaryButtonOutline
                        className="!h-[30px] !min-h-[30px] w-full max-w-[102px] !p-1"
                        onClick={() => {
                          openModal(<ActivePlayerModel activePlayer={user} />);
                        }}
                      >
                        View more
                      </PrimaryButtonOutline>
                    )}
                  </div>
                </SwiperSlide>
              ))}
              {/* </div> */}
            </Swiper>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ActivePlayerSlider;
