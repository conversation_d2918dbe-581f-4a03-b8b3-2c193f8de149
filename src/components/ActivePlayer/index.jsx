'use client';

import React, { useState } from 'react';
import Tabs from '@/components/Common/Tabs/ActivePlayerTab/index';
import SelfExclusion from '../ResponsibleGaming/SelfExclusion';
import ActivePlayerSlider from '../ActivePlayerSlider';
import useAuthStore from '@/store/useAuthStore';

const ActivePlayer = () => {
  // const [activeTab, setActiveTab] = useState(0);
  
  const { isAuthenticated } = useAuthStore();
  console.log("🚀 ~ ActivePlayer ~ isAuthenticated:", isAuthenticated)
  const taskListTabs = [
    {
      label: 'My Friends',
      content: (
        <ActivePlayerSlider
          tab={'friends'}
          // activeTab={activeTab}
          // setActiveTab={setActiveTab}
        />
      ),
    },
    {
      label: 'Public',
      content: (
        <ActivePlayerSlider
          tab={'public'}
          // activeTab={activeTab}
          // setActiveTab={setActiveTab}
        />
      ),
      // content: <SelfExclusion />,
    },
  ];
  if (!isAuthenticated) {
    return null;
  }
  return (
    <div className="relative mb-9 mt-10 rounded-lg pb-4">
      <div className="flex flex-col items-start ">
        <Tabs
          tabs={taskListTabs}
          // activeTab={activeTab}
          // setActiveTab={setActiveTab}
        />
      </div>
    </div>
  );
};

export default ActivePlayer;
