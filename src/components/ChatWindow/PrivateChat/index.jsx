'use client';

import CallIcon from '@/assets/icons/CallIcon';
import CloseIcon from '@/assets/icons/CloseIcon';
import DisconnectIcon from '@/assets/icons/DisconnectIcon';
import useAuthStore from '@/store/useAuthStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import { formatDateTime, getAccessToken, isValidURL } from '@/utils/helper';
import AgoraRTC from 'agora-rtc-sdk-ng';
import { ChevronLeft, Mic, MicOff } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useRef } from 'react';

import ChatAvatar from '@/components/ChatWindow/ChatAvatar';
import usePrivateCall, { rtc } from '@/hooks/usePrivateCall';
import useCallStore from '@/store/useCallStore';
import { chatRoomSocket } from '@/utils/socket';
import IconButton from '../../Common/Button/IconButton';

export default function PrivateChat(props) {
  const { privateChat, recipientUser, privateChatUserDetails } = props;
  console.log(
    '🚀 ~ PrivateChat ~ privateChat, recipientUser, privateChatUserDetails:',
    privateChat,
    recipientUser,
    privateChatUserDetails,
  );
  const {
    isPrivateChatOpen,
    setIsPrivateChatOpen,
    isCallActive,
    setIsCallActive,
    callId,
  } = usePrivateChatStore((state) => state);
  const { userDetails } = useAuthStore((state) => state);

  const chatContainerRef = useRef(null);
  AgoraRTC.setLogLevel(4);
  const accessToken = getAccessToken();
  // const [isCallActive, setIsCallActive] = useState(false);
  console.log('its here ?????????? 1111', privateChat);
  // const [isMuted, setIsMuted] = useState(false);
  const { isMuted, setIsMuted, initiateCall, disconnectCall } =
    usePrivateCall();
  const { toggleMuted, setToggleMuted } = useCallStore();

  useEffect(() => {
    chatRoomSocket.auth = { token: accessToken };
    chatRoomSocket.connect();
    // voiceCallConnected.auth = { token: accessToken };
    // voiceCallConnected.connect();
    // chatRoomSocket.on('DECLINE_PERSONAL_VOICE_CHAT_CHANNEL', disconnectCall);
    // chatRoomSocket.on('NOT_ATTENDED_VOICE_CALL', disconnectCall);
  }, []);

  const toggleMicrophone = async () => {
    if (rtc.localAudioTrack) {
      if (toggleMuted) {
        await rtc.localAudioTrack.setEnabled(true);
      } else {
        await rtc.localAudioTrack.setEnabled(false);
      }
      setToggleMuted(!toggleMuted);
    }
  };

  const closePrivateChatModal = () => {
    setIsPrivateChatOpen(false);
  };

  useEffect(() => {
    if (!rtc.client) return;

    rtc.client.on('user-published', async (user, mediaType) => {
      await rtc.client.subscribe(user, mediaType);
      if (mediaType === 'audio') {
        rtc.remoteAudioTrack = user.audioTrack;
        rtc.remoteAudioTrack.play();
      }
    });

    rtc.client.on('user-unpublished', (user, mediaType) => {
      if (mediaType === 'audio') {
        if (rtc.remoteAudioTrack) {
          rtc.remoteAudioTrack.stop();
          rtc.remoteAudioTrack = null;
        }
      }
    });

    return () => {
      rtc.client.removeAllListeners();
      setIsMuted(false);
    };
  }, []);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [privateChat]);

  return isPrivateChatOpen ? (
    <div className="flex h-full w-full flex-col">
      <div className="flex items-center justify-between gap-2 bg-oxfordBlue-1000 px-4 py-2 shadow-chat-header">
        <div className="flex w-full items-center justify-between">
          <ChevronLeft
            className="cursor-pointer text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
            onClick={closePrivateChatModal}
          />
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-4 whitespace-nowrap">
              <div className="relative h-10 w-10 shrink-0">
                <ChatAvatar
                  profileImage={recipientUser?.recipientProfileImage}
                  firstName={recipientUser?.firstName}
                  lastName={recipientUser?.lastName}
                  imageClassName="h-full w-full object-cover rounded-full border-2 border-oxfordBlue-1000"
                  imageWidth={40}
                  imageHeight={40}
                  avatarSize={40}
                />
              </div>
              <p className="max-w-[150px] truncate text-xl">
                {recipientUser?.recipientUsername ||
                  privateChatUserDetails?.username}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {(privateChatUserDetails?.areFriends ||
              recipientUser?.areFriends) && (
              <>
                {!isCallActive ? (
                  <IconButton
                    onClick={() =>
                      initiateCall(
                        privateChatUserDetails?.userId ||
                          recipientUser?.recipientId,
                      )
                    }
                    className="h-6 w-6"
                  >
                    <CallIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 hover:fill-white-1000" />
                  </IconButton>
                ) : (
                  // )
                  (console.log(
                    'callid',
                    callId,
                    privateChatUserDetails?.userId,
                    recipientUser?.recipientId,
                  ),
                  callId ==
                    (privateChatUserDetails?.userId ||
                      recipientUser?.recipientId) && (
                    <>
                      <IconButton onClick={disconnectCall} className="h-6 w-6">
                        <DisconnectIcon
                          className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800"
                          fill="red"
                        />
                      </IconButton>
                      <IconButton
                        onClick={toggleMicrophone}
                        className="h-6 w-6"
                      >
                        {toggleMuted ? (
                          <MicOff className="h-5 w-5 text-red-500 transition-all duration-300 hover:text-red-600" />
                        ) : (
                          <Mic className="h-5 w-5 text-steelTeal-1000 transition-all duration-300 hover:text-white-1000" />
                        )}
                      </IconButton>
                    </>
                  ))
                )}
              </>
            )}

            <IconButton onClick={closePrivateChatModal} className="h-6 w-6">
              <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
        </div>
      </div>

      <div className="flex min-h-0 shrink grow basis-[0%] flex-col">
        <div
          ref={chatContainerRef}
          className="scrollbar-none flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden px-[0.625rem]"
        >
          <div className="flex flex-col gap-[0.625rem]">
            {privateChat?.map((chat) => {
              const received =
                Number(chat?.userId) !== Number(userDetails?.userId);
              return (
                <div
                  key={chat?.createdAt}
                  className={`flex ${received ? '' : 'justify-end'} gap-[0.625rem] py-[0.625rem] pl-[0.625rem]`}
                >
                  <div className="flex w-[calc(100%_-_3.625rem)] flex-col gap-3">
                    <div className="flex w-full flex-col gap-1">
                      <h6
                        className={`flex ${received ? '' : 'justify-end'} gap-[0.625rem] text-[0.813rem] font-normal leading-none text-steelTeal-1000`}
                      >
                        {received && (
                          <span className="inline-block truncate">
                            @{chat?.user?.username}
                          </span>
                        )}
                        <span className="inline-block">
                          {formatDateTime(chat?.createdAt)}
                        </span>
                      </h6>
                      {chat?.message ? (
                        isValidURL(chat?.message) ? (
                          <Image
                            src={chat?.message}
                            width={10000}
                            height={10000}
                            className="w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                            alt="GIF"
                          />
                        ) : (
                          <p className="rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000">
                            {chat?.message}
                          </p>
                        )
                      ) : null}
                      {chat?.image && (
                        <Image
                          src={chat?.image}
                          width={10000}
                          height={10000}
                          className="h-auto w-full max-w-full"
                          alt="Chat Image"
                        />
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  ) : null;
}
