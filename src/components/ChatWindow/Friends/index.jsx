import { useState } from 'react';
import toast from 'react-hot-toast';
import { <PERSON><PERSON>he<PERSON>, CircleX } from 'lucide-react';
import useUserInfoStore from '@/store/useUserInfoStore';
import DeleteUserIcon from '@/assets/icons/Delete-User';
import PrivateChatIcon from '@/assets/icons/PrivateChat';
import SearchIcon from '@/assets/icons/Search';
import {
  useGetFriendsListQuery,
  useGetFriendsRequestListQuery,
  useUnFriendsRequest,
  useUpdateFriendsRequest,
} from '@/reactQuery/chatWindowQuery';
import Tooltip from '@/components/Common/Tooltip';
import ArrowDownIcon from '@/assets/icons/ArrowDownIcon';
import FriendChatIcon from '@/assets/icons/FriendChatIcon';
import SmallChatIcon from '@/assets/icons/SmallChatIcon';
import CallIcon from '@/assets/icons/CallIcon';
import useModalStore from '@/store/useModalStore';
import UserInfo from '@/components/UserInfoModal';
import ChatAvatar from '@/components/ChatWindow/ChatAvatar';
import CustomAccordion from '../Accordian';
import FriendGoldenIcon from '@/assets/icons/FriendGoldenIcon';

export default function Friends({ setPrivateChatUserDetails }) {

  const [search, setSearch] = useState('');
  const { openUserInfoModal } = useUserInfoStore((state) => state);
  const { openModal } = useModalStore((state) => state);

  const { data: friendsList, refetch: refetchFriendsList } =
    useGetFriendsListQuery({ params: { search } });
  const { data: friendsRequestList, refetch: refetchFriendsRequestList } =
    useGetFriendsRequestListQuery({});

  const [openIndex, setOpenIndex] = useState(friendsRequestList?.rows?.length > 0 ? 1 : 0);

  const toggleAccordion = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };


  const mutationUpdateRequest = useUpdateFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      refetchFriendsList();
      refetchFriendsRequestList();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetchFriendsList();
      refetchFriendsRequestList();
    },
  });

  const mutationUnFriendRequest = useUnFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      refetchFriendsList();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetchFriendsList();
    },
  });

  const handleFriendRequest = (requestId, status) => {
    mutationUpdateRequest.mutate({
      requestId,
      status,
    });
  };

  const unFriend = (unfriendUserId) => {
    mutationUnFriendRequest.mutate({ unfriendUserId: +unfriendUserId });
  };
  const handleOpenUserInfoModal = (userId) => {
    console.log('userId', userId);

    openUserInfoModal(userId);
    openModal(
      <UserInfo setPrivateChatUserDetails={setPrivateChatUserDetails} />,
    );
  };

  return (
    <div className="flex h-full flex-col px-2.5 pt-2.5 z-[1]">
      {/* Accordion 1 */}
      <div className="">
        {/* Accordion Header */}
        <button
          onClick={() => toggleAccordion(0)}
          className={`flex w-full items-center justify-between border-b border-solid px-5 py-2.5 focus:outline-none ${openIndex === 0 ? 'border-borderColor-100' : 'border-gray-700'}`}
        >
          <div className="flex flex-row gap-2 items-center justify-center">
            {<FriendChatIcon /> }
            <div className="">Friends ({friendsList?.rows?.length})</div>
          </div>

          <ArrowDownIcon
            className={`transition-all duration-300 ease-in-out ${openIndex === 0 ? 'rotate-180' : ''}`}
          />
        </button>

        {/* Accordion Content */}
        <div
          className={`overflow-hidden transition-[max-height] duration-300 ${
            openIndex === 0 ? 'max-h-screen' : 'max-h-0'
          }`}
        >
          <div className="px-2 py-1">
            <div className="mb-1 flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
              <SearchIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
              <textarea
                className="h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
                placeholder="Search"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <section className="max-h-[218px] overflow-y-auto pt-1">
              <div className="flex flex-col gap-2">
                {friendsList?.rows?.length > 0 ? (
                  friendsList?.rows?.map((friend) => {
                    return (
                      <div
                        key={friend.relationUser?.userId}
                        className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
                      >
                        <div className="flex flex-row gap-2.5">
                          <div
                            className="relative h-12 w-12 min-w-12 cursor-pointer"
                            onClick={() =>
                              handleOpenUserInfoModal(
                                friend.relationUser?.userId,
                              )
                            }
                          >
                            <ChatAvatar
                              profileImage={friend?.relationUser?.profileImage}
                              firstName={friend?.relationUser?.firstName}
                              lastName={friend?.relationUser?.lastName}
                              imageClassName="h-full w-full rounded-full object-cover object-center"
                              imageWidth={48}
                              imageHeight={48}
                              avatarSize={48}
                            />
                          </div>
                          <div className="mt-4 flex justify-center">
                            {friend.relationUser?.username}
                          </div>
                        </div>
                        <Tooltip text="Un-friend" position="left">
                          <DeleteUserIcon
                            onClick={() => unFriend(friend.relationUser.userId)}
                            className="w-7 cursor-pointer fill-steelTeal-1000 hover:fill-white-1000"
                            alt="UnFriend"
                          />
                        </Tooltip>
                      </div>
                    );
                  })
                ) : (
                  <div className="mt-10 flex justify-center">No Friends</div>
                )}
              </div>
            </section>
          </div>
        </div>
      </div>

      {/* Accordion 2 */}
      <div className="">
        {/* Accordion Header */}
        <button
          onClick={() => toggleAccordion(1)}
          className={`flex w-full items-center justify-between border-b border-solid px-5 py-2.5 focus:outline-none ${openIndex === 1 ? 'border-borderColor-100' : 'border-gray-700'}`}
        >
          <div className="flex flex-row gap-2">
            <FriendGoldenIcon
              width="20"
              height="20"
            />
            <div className="">
              Friends Requests ({friendsRequestList?.rows?.length})
            </div>
          </div>

          <ArrowDownIcon
            className={`transition-all duration-300 ease-in-out ${openIndex === 1 ? 'rotate-180' : ''}`}
          />
        </button>

        {/* Accordion Content 2 */}
        <div
          className={`overflow-hidden transition-[max-height] duration-300 ${
            openIndex === 1 ? 'max-h-screen' : 'max-h-0'
          }`}
        >
          <div className="px-2 py-1">
            <div className="mb-1 flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
              <SearchIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
              <textarea
                className="h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
                placeholder="Search"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>

            <section className="h-1/2 overflow-y-auto p-2">
              <div className="flex flex-col gap-2">
                {friendsRequestList?.rows?.length > 0 ? (
                  friendsRequestList?.rows?.map((friendRequest) => {
                    const userId = friendRequest.userFriendRequester?.userId;

                    return (
                      <div
                        key={userId}
                        className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
                      >
                        <div className="flex flex-row gap-2">
                          <div
                            className="relative h-12 w-12 min-w-12 cursor-pointer"
                            onClick={() => openUserInfoModal(userId)}
                          >
                            <ChatAvatar
                              profileImage={friendRequest?.userFriendRequester?.profileImage}
                              firstName={friendRequest?.userFriendRequester?.firstName}
                              lastName={friendRequest?.userFriendRequester?.lastName}
                              imageClassName="h-full w-full rounded-full object-cover object-center"
                              imageWidth={48}
                              imageHeight={48}
                              avatarSize={48}
                            />
                          </div>
                          <div className="mt-4">
                            {friendRequest.userFriendRequester?.username}
                          </div>
                        </div>
                        <div className="z-10 flex flex-row gap-2">
                          <Tooltip text="Accept" position="left">
                            <CircleCheck
                              onClick={() =>
                                handleFriendRequest(
                                  friendRequest.requestId,
                                  'accepted',
                                )
                              }
                              className="w-7 cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                            />
                          </Tooltip>
                          <Tooltip text="Reject" position="left">
                            <CircleX
                              onClick={() =>
                                handleFriendRequest(
                                  friendRequest.requestId,
                                  'rejected',
                                )
                              }
                              className="w-7 cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                            />
                          </Tooltip>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="mt-10 flex justify-center">
                    No friend requests
                  </div>
                )}
              </div>
            </section>
          </div>
        </div>
      </div>

      {/* <section className="mt-1 p-2">

        <div className="m-2 flex flex-row gap-2">
          <PrivateChatIcon className="fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
          <div className="">Friends ({friendsList?.rows?.length})</div>
        </div>
        <hr className="my-1 h-px border-0 bg-gray-200 dark:bg-gray-700" />

        <div className="mb-1 flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
          <SearchIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
          <textarea
            className="h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
            placeholder="search"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
      </section>

      <section className="h-1/2 overflow-y-auto p-2">
        <div className="flex flex-col gap-2">
          {friendsList?.rows?.length > 0 ? (
            friendsList?.rows?.map((friend) => {
              const profile_img = friend?.relationUser?.profileImage || profile_url;
              return (
                <div
                  key={friend.relationUser?.userId}
                  className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
                >
                  <div className="flex flex-row gap-2.5">
                    <div
                      className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full border-2 border-oxfordBlue-1000"
                      onClick={() =>
                        openUserInfoModal(friend.relationUser?.userId)
                      }
                    >
                      <Image
                        src={profile_img}
                        width={10000}
                        height={10000}
                        className="h-full w-full max-w-full rounded-full object-cover object-center"
                        alt="Profile"
                      />
                    </div>
                    <div className="mt-4 flex justify-center">
                      {friend.relationUser?.username}
                    </div>
                  </div>
                  <Tooltip text="Un-friend" position="left">
                    <DeleteUserIcon
                      onClick={() => unFriend(friend.relationUser.userId)}
                      className="w-7 cursor-pointer fill-steelTeal-1000 hover:fill-white-1000"
                      alt="UnFriend"
                    />
                  </Tooltip>
                </div>
              )
            })
          ) : (
            <div className="mt-10 flex justify-center">No Friends</div>
          )}
        </div>
      </section> */}

      {/* <section className="mt-1 p-2">
        <div className="m-2 flex flex-row gap-2">
          <PrivateChatIcon className="fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
          Application List ({friendsRequestList?.rows?.length})
        </div>
        <hr className="my-1 h-px border-0 bg-gray-200 dark:bg-gray-700" />
      </section>

      <section className="h-1/2 overflow-y-auto p-2">
        <div className="flex flex-col gap-2">
          {friendsRequestList?.rows?.length > 0 ? (
            friendsRequestList?.rows?.map((friendRequest) => {
              const profileImg = friendRequest?.userFriendRequester?.profileImage || profile_url; // Fallback to default image

              return (
                <div
                  key={friendRequest.userFriendRequester?.userId}
                  className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
                >
                  <div className="flex flex-row gap-2">
                    <div
                      className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full border-2 border-oxfordBlue-1000"
                      onClick={() =>
                        openUserInfoModal(
                          friendRequest.userFriendRequester?.userId,
                        )
                      }
                    >
                      <Image
                        src={profileImg}
                        width={10000}
                        height={10000}
                        className="h-full w-full max-w-full rounded-full object-cover object-center"
                        alt="Profile"
                      />
                    </div>
                    <div className="mt-4">
                      {friendRequest.userFriendRequester?.username}
                    </div>
                  </div>
                  <div className="flex flex-row gap-2 z-10">
                    <Tooltip text="Accept" position="top" >
                      <CircleCheck
                        onClick={() =>
                          handleFriendRequest(friendRequest.requestId, 'accepted')
                        }
                        className="w-7 cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                      />
                    </Tooltip>
                    <Tooltip text="Reject" position="top">
                      <CircleX
                        onClick={() =>
                          handleFriendRequest(friendRequest.requestId, 'rejected')
                        }
                        className="w-7 cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                      />
                    </Tooltip>
                  </div>
                </div>
              )
            })
          ) : (
            <div className="mt-10 flex justify-center">No friend requests</div>
          )}
        </div>
      </section> */}
    </div>
  );
}
