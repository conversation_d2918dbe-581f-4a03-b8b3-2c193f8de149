import ArrowDownIcon from '@/assets/icons/ArrowDownIcon';
import MessageIcon from '@/assets/icons/MessageIcon';
import IconButton from '@/components/Common/Button/IconButton';
import { forwardRef } from 'react';
import ChatAvatar from '@/components/ChatWindow/ChatAvatar';
import CallIcon from '@/assets/icons/CallIcon';
import DisconnectIcon from '@/assets/icons/DisconnectIcon';
import useGroupCall from '@/hooks/useGroupCall';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import { useRouter } from 'next/navigation';
import GroupImg1 from '../../../assets/images/demo-image/user-profile.jpg';
import usePrivateCall from '@/hooks/usePrivateCall';

// other imports

const GroupsListing = forwardRef(
  (
    {
      name,
      toggleAccordion,
      data,
      openChat,
      handleOpenModal,
      openIndex,
      toggleGroup,
      joinGroup,
      showLoading,
      searchLoading,
    },
    lastGroupElementRef,
    lastGroupElementGroupRef,
  ) => {
    const router = useRouter();
    const { isGroupPageOpen } = useGroupChatStore((state) => state);
    const { initiateCall, disconnectCall, isCallActive, handleDeclineCall } =
      useGroupCall();
    const { isCallActive: isPrivateCallActive } = usePrivateCall();
    console.log('🚀 ~ isCallActive:', isCallActive);
    const { voiceCall } = useVoiceCallStore((state) => state);
    console.log('🚀 ~ voiceCall:', voiceCall);

    const handleOpenChat = (id, name) => {
      openChat(id, name);
      if (isGroupPageOpen) {
        router.push(
          `/group/${encodeURIComponent(name)
            .replace(/%20/g, '-')
            .toLowerCase()}`,
        );
      }
    };

    return (
      <div className="">
        {/* Accordion Header */}
        <button
          onClick={() => toggleAccordion(1)}
          className={`flex w-full items-center justify-between border-b border-solid px-5 py-2.5 focus:outline-none ${openIndex === 0 ? 'border-borderColor-100' : 'border-gray-700'}`}
        >
          <div className="flex flex-row gap-2">
            <div>
              {name} ({searchLoading ? 0 : data?.total})
            </div>
          </div>
          <ArrowDownIcon
            className={`transition-all duration-300 ease-in-out ${openIndex === 1 ? 'rotate-180' : ''}`}
          />
        </button>

        {/* Accordion Content */}
        <div
          className={` transition-[max-height] duration-300 ${openIndex === 1 ? 'max-h-screen' : 'max-h-0 overflow-hidden'}`}
        >
          <div className="py-1">
            <section className="overflow-y-auto pt-1">
              <div className="md:pd-0 flex flex-col pb-8 sm:pb-[36px]">
                {searchLoading ? (
                  <div className="text-white text-center">Loading...</div>
                ) : data?.groups?.length === 0 ? (
                  <div className="text-white text-center">No groups found</div>
                ) : (
                  data?.groups.map((group, index) => {
                    const isLast = index === data.groups.length - 1;
                    return (
                      <div
                        key={group.id}
                        ref={
                          isLast
                            ? lastGroupElementRef || lastGroupElementGroupRef
                            : null
                        }
                        className="mb-2 flex cursor-pointer items-center  gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 pb-4 pl-[18px] pr-2 pt-2"
                        onClick={() =>
                          handleOpenChat(group.id, group.groupName)
                        }
                      >
                        <div className="flex w-full justify-between">
                          <div className="flex gap-[0.625rem]">
                            <div className="relative">
                              <div
                                className="flex h-12 w-12 flex-wrap overflow-hidden rounded-full bg-white-1000"
                                onClick={(e) =>
                                  handleOpenModal(e, group.id, group)
                                }
                              >
                                <ChatAvatar
                                  profileImage={group.profile}
                                  firstName={group?.groupName}
                                  lastName=""
                                  imageClassName="h-12 w-12 object-contain"
                                  imageWidth={48}
                                  imageHeight={48}
                                  avatarSize={48}
                                />
                              </div>
                              <span className="text-white absolute -left-1.5 bottom-[-7px] flex h-6 w-6 items-center justify-center rounded-full border border-solid border-oxfordBlue-900 bg-scarlet-900 text-sm font-bold">
                                {group.groupMembersCount}
                              </span>
                            </div>
                            <div className="flex flex-col justify-center">
                              <div className="text-white-1000">
                                {group.groupName}
                              </div>
                              {group.groupMembersCount > 0 && (
                                <div className="text-[13px] font-[400] text-[#999999]">
                                  {group.groupMembersCount} Members
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            {toggleGroup === 'PUBLIC_GROUPS' &&
                            !group?.isUserExistInGroup ? (
                              <button
                                disabled={joinGroup?.isPending}
                                className="text-white flex items-center justify-center gap-2 rounded-2xl bg-primary-1000 px-4 py-2 text-[15px] font-semibold capitalize leading-none transition duration-200 hover:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-700"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  joinGroup.mutate({
                                    groupId: group.id,
                                    action: 'join',
                                  });
                                }}
                              >
                                Join
                              </button>
                            ) : (
                              <div className="flex gap-1">
                                {!isPrivateCallActive &&
                                  (voiceCall?.channelName ? (
                                    voiceCall?.channelName ==
                                    group.groupName ? (
                                      <IconButton
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDeclineCall();
                                        }}
                                        className="h-6 w-6"
                                      >
                                        <DisconnectIcon
                                          className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800"
                                          fill="red"
                                        />
                                      </IconButton>
                                    ) : (
                                      ''
                                    )
                                  ) : !isCallActive ? (
                                    <IconButton
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        initiateCall({
                                          groupId: group.id,
                                          groupName: group.groupName,
                                        });
                                      }}
                                      className="h-6 w-6"
                                    >
                                      <CallIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 hover:fill-white-1000" />
                                    </IconButton>
                                  ) : (
                                    // )
                                    <IconButton
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleDeclineCall();
                                      }}
                                      className="h-6 w-6"
                                    >
                                      <DisconnectIcon
                                        className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800"
                                        fill="red"
                                      />
                                    </IconButton>
                                  ))}

                                {/* Message Icon */}
                                <IconButton className="h-6 w-6 min-w-6">
                                  <MessageIcon />
                                </IconButton>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })
                )}
                {showLoading && (
                  <div className="text-white py-2 text-center">
                    Loading more groups...
                  </div>
                )}
              </div>
            </section>
          </div>
        </div>
      </div>
    );
  },
);

export default GroupsListing;
