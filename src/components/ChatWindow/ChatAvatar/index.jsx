import React, { useMemo, useState } from 'react';
import Image from 'next/image';
import UserAvatar from '@/components/DefaultImage';
import { failedImageCache } from '@/utils/helper';

const ChatAvatar = ({
  profileImage,
  firstName,
  lastName,
  imageClassName = '',
  alt = '',
  imageWidth = 0,
  imageHeight = 0,
  avatarSize = 0
}) => {
  const [imgError, setImgError] = useState(false);

  // Use useMemo to decide image validity before rendering
  const shouldShowImage = useMemo(() => {
    return profileImage && !failedImageCache.has(profileImage);
  }, [profileImage]);

  const handleError = () => {
    setImgError(true);
    failedImageCache.add(profileImage);
  };

  return shouldShowImage && !imgError ? (
    <Image
      key={profileImage} // force remount if src changes
      src={profileImage}
      alt={alt}
      width={imageWidth}
      height={imageHeight}
      className={imageClassName}
      loading="eager"
      onError={handleError}
    />
  ) : (
    <UserAvatar
      firstName={firstName}
      lastName={lastName}
      size={avatarSize}
    />
  );
};

export default ChatAvatar;
