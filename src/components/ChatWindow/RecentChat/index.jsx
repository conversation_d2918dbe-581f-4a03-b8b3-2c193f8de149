/* eslint-disable no-nested-ternary */

'use client';

import React, { useState } from 'react';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import SearchIcon from '@/assets/icons/Search';
import { useGetRecentChatsQuery } from '@/reactQuery/chatWindowQuery';
import MainLoader from '@/components/Common/Loader/MainLoader';
import NoDataFound from '@/components/Common/NoDataFound';
import useIgnoredUsers from '@/hooks/useIgnoredUsers';
import IconButton from '@/components/Common/Button/IconButton';
import MessageIcon from '@/assets/icons/MessageIcon';
import ChatAvatar from '../ChatAvatar';
import useModalStore from '@/store/useModalStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import UserInfo from '@/components/UserInfoModal';

function RecentChat() {
  const [search, setSearch] = useState('');
  const { data: recentChats, isLoading } = useGetRecentChatsQuery({
    params: {
      page: 1,
      limit: 400,
      search: search || '',
    },
  });
  const { setIsPrivateChatOpen, setUserId } = usePrivateChatStore(
    (state) => state,
  );
  const { ignoredUsers } = useIgnoredUsers();
  const { openModal } = useModalStore((state) => state);
  const { openUserInfoModal } = useUserInfoStore((state) => state);

  const openChat = (userId) => {
    setUserId(userId);
    setIsPrivateChatOpen(true);
  };

  const handleOpenUserInfoModal = (userId) => {
    openUserInfoModal(userId);
    openModal(<UserInfo />);
  };
  // publicChats.filter(d=>!( ignoredUsers?.rows?.map(row=>row?.relationUserId)?.includes( Number(d?.userId||d?.id))))
  return (
    <div className="flex flex-col gap-2 pb-[46px]  md:p-2 md:pb-[46px] lg:pb-2">
      <div className="flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1 shadow-[0px_-4px_10px_var(--richBlack-1000)]">
        <SearchIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
        <div className="relative w-full">
          <input
            className="h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] pr-8 py-2 placeholder:text-steelTeal-1000"
            placeholder="Search"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          {search && (
            <span
              className="absolute right-2 top-1/2 -translate-y-1/2 transform cursor-pointer text-gray-500 hover:text-gray-700"
              onClick={() => setSearch('')}
              style={{ fontSize: '16px' }}
            >
              &#x2715;
            </span>
          )}
        </div>
      </div>
      {isLoading ? (
        <div className="mt-8 flex w-full items-center justify-center">
          <MainLoader className="w-20" />
        </div>
      ) : recentChats?.length ? (
        recentChats
          ?.filter(
            (d) =>
              !ignoredUsers?.rows
                ?.map((row) => row?.relationUserId)
                ?.includes(Number(d?.recipient_id)),
          )
          ?.map((recentChat) => {
            return (
              <div
                key={`${recentChat?.['recipientUser.receiver_name']}${recentChat?.actionee_id}`}
                className="flex cursor-pointer justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2 items-center"
                onClick={() => openChat(recentChat?.recipient_id)}
                onKeyDown={() => {}}
                role="button"
                tabIndex="0"
              >
                <div className="flex flex-row gap-2.5">
                  <div
                    className="relative h-12 w-12 min-w-12 rounded-full border-oxfordBlue-1000 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenUserInfoModal(recentChat?.recipient_id);
                    }}
                  >
                    <ChatAvatar
                      alt="Profile"
                      profileImage={recentChat?.['recipientUser.profile_image']}
                      firstName={
                        recentChat['recipientUser.first_name'] ||
                        recentChat['user.username']
                      }
                      lastName={recentChat['recipientUser.last_name']}
                      imageClassName="h-full w-full max-w-full rounded-full object-cover object-center !leading-loose"
                      imageWidth={44}
                      imageHeight={44}
                      avatarSize={45}
                    />
                    <span className="absolute -bottom-1 -left-1.5 flex h-6 w-6 items-center justify-center rounded-full border-2 border-oxfordBlue-1000 bg-primary-1000 pt-1 text-center text-xs leading-none">
                      {recentChat?.['recipientUser.vip_level']}
                    </span>
                  </div>
                  <div className="mt-4 flex justify-center">
                    {recentChat?.['recipientUser.receiver_name']}
                  </div>
                </div>
                <IconButton className="h-6 w-6 min-w-6">
                  <MessageIcon />
                </IconButton>
              </div>
            );
          })
      ) : (
        <div className="flex h-full w-full items-center justify-center">
          <NoDataFound className="mt-20 w-28" />
        </div>
      )}
    </div>
  );
}

export default RecentChat;
