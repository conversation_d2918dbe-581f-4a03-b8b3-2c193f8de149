'use client';

import ArrowCircleDownIcon from '@/assets/icons/Arrow-Circle-Down';
import ArrowCircleLeftIcon from '@/assets/icons/Arrow-Circle-Left';
import OnlineUserImg from '@/assets/images/demo-image/user-img.jpg';
import useAuthStore from '@/store/useAuthStore';
import useModalStore from '@/store/useModalStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import useEmblaCarousel from 'embla-carousel-react';
import { Mic, MicOff } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useEffect, useRef, useState } from 'react';
import 'swiper/css';
import 'swiper/css/navigation';
import { Mousewheel } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import GameCard from '../Common/GameCard';
import ConnectedPlayModel from './ConnectedPlayModal';
import { userStatusColor } from '@/utils/helper';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import { useGameUtils } from '@/utils/game';
import ChatAvatar from '../ChatWindow/ChatAvatar';

function ConnectedPlaySlider({
  className = '',
  gamesList,
  id,
  isAuthenticated,
  tab,
  ...props
}) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    slidesToScroll: 2,
    align: 'start',
    dragFree: false,
  });
  const { connectedPlay, setConnectedPlay } = useActiveGroupStore(
    (state) => state,
  );
  const { handleClickGame } = useGameUtils();

  const { openModal } = useModalStore();
  const { openUserInfoModal } = useUserInfoStore((state) => state);
  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    // setSelectedIndex(emblaApi.selectedScrollSnap());
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  const { userDetails } = useAuthStore((state) => state);
  console.log('🚀 ~ userDetails:', userDetails);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const swiperRef = useRef(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  const handleSlidePrev = () => {
    if (swiperRef.current) {
      swiperRef.current.slideTo(
        Math.max(swiperRef.current.activeIndex - (isMobile ? 2 : 5), 0),
      );
    }
  };

  const handleSlideNext = () => {
    if (swiperRef.current) {
      swiperRef.current.slideTo(
        swiperRef.current.activeIndex + (isMobile ? 2 : 5),
      );
    }
  };

  return (
    <div className="max-w-containerWidth relative mx-auto mt-3 w-full ">
      {/* {connectedPlayUsers?.length > 0 && (
        <div className="absolute right-0 top-7 flex flex-row items-center gap-2 ">
          <div className="flex gap-2">
            <button
              onClick={handleSlidePrev}
              disabled={isBeginning || noNavigationNeeded}
            >
              <ArrowCircleLeftIcon
                className={`size-7 fill-steelTeal-1000 transition-opacity ${
                  isBeginning || noNavigationNeeded
                    ? 'cursor-not-allowed opacity-30'
                    : 'cursor-pointer'
                }`}
              />
            </button>

            <button
              onClick={handleSlideNext}
              disabled={isEnd || noNavigationNeeded}
            >
              <ArrowCircleDownIcon
                className={`size-7 -rotate-90 fill-steelTeal-1000 transition-opacity ${
                  isEnd || noNavigationNeeded
                    ? 'cursor-not-allowed opacity-30'
                    : 'cursor-pointer'
                }`}
              />
            </button>
          </div>
       
        </div>
      )} */}
      {connectedPlay?.games?.length > 0 && (
        <button
          className="absolute left-0 top-[-10px] z-10 -translate-y-1/2"
          onClick={handleSlidePrev}
          disabled={isBeginning}
        >
          <ArrowCircleLeftIcon
            className={`size-8 fill-steelTeal-1000 transition-opacity ${
              isBeginning ? 'cursor-not-allowed opacity-30' : 'cursor-pointer'
            }`}
          />
        </button>
      )}

      <div className={`${className}`}>
        <div className="embla m-auto">
          <div
          // className="embla__viewport overflow-hidden  pb-7 pt-5 max-md:pb-4"
          // ref={emblaRef}
          >
            <Swiper
              breakpoints={{
                0: {
                  slidesPerView: 2,
                  slidesPerGroup: 2,
                  spaceBetween: 7,
                },
                500: {
                  slidesPerView: 3,
                  slidesPerGroup: 3,
                  spaceBetween: 14,
                },
                768: {
                  slidesPerView: 4,
                  slidesPerGroup: 4,
                  spaceBetween: 14,
                },
                1024: {
                  slidesPerView: 5,
                  slidesPerGroup: 5,
                  spaceBetween: 14,
                },
              }}
              modules={[Mousewheel]}
              mousewheel={{ forceToAxis: true }}
              onSwiper={(swiper) => {
                swiperRef.current = swiper;
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              onSlideChange={(swiper) => {
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              className="!px-1"
            >
              {/* <div className=""> */}
              {connectedPlay?.games?.map((game) => (
                <SwiperSlide
                  key={game?.id}
                  style={{ overflow: 'visible', padding: '10px 0' }}
                >
                  <div className="">
                    <div className="relative rounded-lg shadow-lg ">
                      <GameCard
                        src={game?.gameUrl}
                        width={200}
                        height={200}
                        gameId={game?.gameId}
                        isFavorite={game?.isFavorite}
                        alt={game?.gameName}
                        onClick={() =>
                          handleClickGame(game?.gameName, game?.provider)
                        }
                      />

                      <div className="bg-red-850/90 text-white absolute bottom-0 left-0 flex w-full items-center justify-between px-3 py-2 text-sm backdrop-blur-sm">
                        <span className="font-medium">
                          {game?.users?.length > 0
                            ? game?.users?.length + ' Joined'
                            : 'Recommended'}
                        </span>
                        <span className="mx-2">|</span>
                        <span className="font-medium">Seat Available</span>
                      </div>
                    </div>

                    <div className="py-2">
                      {game?.users?.map(
                        (player, index) =>
                          index < 3 && (
                            <div
                              className="inline-flex w-full max-w-[181px] items-center  justify-between rounded-full border border-solid border-secondaryBorder px-2 py-1"
                              key={index}
                            >
                              <div className="inline-flex items-center gap-2 ">
                              <div className="relative inline-flex size-[30px] shrink-0 cursor-pointer">
                                  <div className="size-[30px] border border-solid border-richBlack-900 rounded-full absolute inset-0 pointer-events-none z-10"></div>
                                  <div
                                    className="size-[30px] rounded-full overflow-hidden"
                                    // onClick={() => {
                                    //   openUserInfoModal(player?.userId);
                                    //   openModal(<UserInfo />);
                                    // }}
                                  >
                                    <ChatAvatar
                                      profileImage={player?.profileImage}
                                      firstName={player?.firstName || player?.username || 'User'}
                                      lastName={player?.lastName || ''}
                                      imageClassName="h-full w-full rounded-full object-cover object-center"
                                      imageWidth={30}
                                      imageHeight={30}
                                      avatarSize={30}
                                    />
                                  </div>

                                  <span
                                    className={`absolute bottom-0  right-0 z-20 size-2 rounded-full ${userStatusColor(player?.currentStatus || 'AVAILABLE')}`}
                                  ></span>
                                </div>
                                <h4 className="text-white max-w-[85px] truncate text-base font-normal">
                                  {player?.username}
                                </h4>
                              </div>
                              {/* {player?.micStatus == 'on' ? <Mic /> : <MicOff />} */}
                            </div>
                          ),
                      )}
                    </div>
                    {game?.users?.length > 3 && (
                      <PrimaryButtonOutline
                        className="!h-[30px] !min-h-[30px] w-full max-w-[102px] !p-1"
                        onClick={() => {
                          openModal(<ConnectedPlayModel data={game} />);
                        }}
                      >
                        View more
                      </PrimaryButtonOutline>
                    )}
                  </div>
                </SwiperSlide>
              ))}
              {/* </div> */}
            </Swiper>
          </div>
        </div>
      </div>
      {connectedPlay?.games?.length > 0 && (
        <button
          className="absolute right-0 top-[-10px] z-10 -translate-y-1/2"
          onClick={handleSlideNext}
          disabled={isEnd}
        >
          <ArrowCircleDownIcon
            className={`size-8 -rotate-90 fill-steelTeal-1000 transition-opacity ${
              isEnd ? 'cursor-not-allowed opacity-30' : 'cursor-pointer'
            }`}
          />
        </button>
      )}
    </div>
  );
}

export default ConnectedPlaySlider;
