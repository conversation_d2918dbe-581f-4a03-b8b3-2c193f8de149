'use client';

import { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { walletSocket } from '@/utils/socket';
import toast from 'react-hot-toast';
import { getAccessToken } from '@/utils/helper';
export function ReactQueryClientProvider({ session, children }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000,
          },
        },
      }),
  );

  const accessToken = getAccessToken();

  function handleNotificationToast(data) {
     if (typeof data?.data?.message == 'object' ? data?.data?.message?.message : data?.data?.message)
      toast.success(typeof data?.data?.message == 'object' ? data?.data?.message?.message : data?.data?.message)
    queryClient.invalidateQueries(['notificationsDetails']);

  }
  useEffect(() => {

    if (!walletSocket.connected) {
      walletSocket.auth = { token: accessToken };
      walletSocket.connect();
    }

    const listener = (data) => {
      handleNotificationToast(data);
    };

    walletSocket.on('NOTIFICATION', listener);

    return () => {
      walletSocket.off('NOTIFICATION', listener);
    };
  }, []); // Keep dependencies clean


  return (
    <QueryClientProvider client={queryClient} contextSharing>
      {children}
    </QueryClientProvider>
  );
}
