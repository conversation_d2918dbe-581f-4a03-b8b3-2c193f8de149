'use client';

import { createVeriff<PERSON>rame } from "@veriff/incontext-sdk";
import { useEffect, useState } from "react";

import useAuthStore from "@/store/useAuthStore";
import AccVerifyQuery from "@/reactQuery/accVerifyQuery";
import InfoModal from "./components/InfoModal";

function Kyc() {
  const [initKYCData, setInitKYCData] = useState(null);
  const [initializeKYC, setInitializeKYC] = useState(false);
  // const userDetails = useUserInfoStore((state) => state.userDetails);
  // const user = useUserInfoStore((state) => state);
  const { userDetails, setUserDetails } = useAuthStore((state) => state);
  // useEffect(() => {
  //   setInitializeKYC(true); // Trigger KYC automatically on page load
  // }, []);

  const errorHandler = (errData) => {
    console.log(errData, ":::::errData")
    if (errData?.errorCode === 3035) {

    } else if (errData?.errorCode === 3072) {

    } else if (errData?.errorCode === 3073) {

    }
  };

  const successHandler = (data) => {
    if (!data?.success) {

    }
  };

  const { data, refetch } = AccVerifyQuery.initKYCQuery({
    enabled: initializeKYC,
    errorHandler,
    successHandler,
  });

  useEffect(() => {
    if (data?.success) {
      setInitKYCData(data?.verification);
    }
  }, [data]);

  useEffect(() => {
    if (initKYCData?.url) {
      const veriffFrame = createVeriffFrame({
        url: initKYCData.url,
        onEvent: function (msg) {
          if (msg === "CANCELED" || msg === "FINISHED") {
            closeVeriffFrame();
            setInitKYCData(null);
            if (msg === "FINISHED") {
              setUserDetails({ ...userDetails, veriffStatus: "REQUESTED" });
            }
          }
        },
      });

      const closeVeriffFrame = () => {
        veriffFrame?.close();
      };

      return () => closeVeriffFrame();
    }
  }, [initKYCData]);

  const handleVeriffKYC = () => {
    setInitializeKYC(true);
    initializeKYC && refetch();
  };

  return (
    <>
      <div id="veriff-root"></div>
      <InfoModal
        title="KYC Verification"
        // handleClose={() => {}}
        textTitle="Complete Your KYC Verification"
        textContent="Kindly upload the required documents to initiate the process. Once approved from our support team, your KYC will be verified."
        handleOnClick={handleVeriffKYC}
        buttonLabel="Start Verification"
      />
    </>
  ); // No UI rendering, just automatic function execution
}

export default Kyc;
