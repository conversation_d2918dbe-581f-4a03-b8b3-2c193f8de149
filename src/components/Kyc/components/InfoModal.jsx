import React from "react";
import PropTypes from "prop-types";
import { X } from 'lucide-react';
import IconButton from "@/components/Common/Button/IconButton";

const InfoModal = ({ title, textTitle, textContent, handleClose, handleOnClick, buttonLabel }) => {
  return (
    <div className="bg-white shadow-lg rounded-lg p-6 max-w-md mx-auto">
      {/* Header */}
     {title !== "KYC Verification" && <div className="flex justify-between items-center border-b pb-3">
        <h2 className="text-lg font-semibold">{title}</h2>
        <IconButton onClick={handleClose} className="h-6 w-6">
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 hover:fill-white" />
        </IconButton>
      </div>}

      {/* Content */}
      <div className="mt-4">
        <h3 className="text-md font-medium">{textTitle}</h3>
        <p className="text-gray-600 mt-2">{textContent}</p>
      </div>

      {/* Button */}
      <div className="mt-6 flex justify-end">
        <button 
          onClick={handleOnClick} 
          className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md transition"
        >
          {buttonLabel}
        </button>
      </div>
    </div>
  );
};

InfoModal.propTypes = {
  title: PropTypes.string.isRequired,
  textTitle: PropTypes.string.isRequired,
  textContent: PropTypes.string.isRequired,
  handleClose: PropTypes.func.isRequired,
  handleOnClick: PropTypes.func.isRequired,
  buttonLabel: PropTypes.string.isRequired,
};

export default InfoModal;
