'use client';

import GreenbarBonus from '@/app/game/GreenbarBonus';
import WalletIcon from '@/assets/icons/WalletIcon';
import useActiveGroups from '@/hooks/useActiveGroup';
import useActivePlayers from '@/hooks/useActivePlayer';
import useChatSection from '@/hooks/useChatSection';
import useConnectedPlay from '@/hooks/useConnecedPlay';
import useHelperHook from '@/hooks/useHelperHook';
import { useUserProfileQuery } from '@/reactQuery/authQuery';
import { useGetBannersQuery } from '@/reactQuery/gamesQuery';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useCallModalStore from '@/store/useCallModalStore';
import useCallStore from '@/store/useCallStore';
import useChatStore from '@/store/useChatStore';
import useGameStore from '@/store/useGameStore';
import useGeneralStore from '@/store/useGeneralStore';
import useGreenBonusStore from '@/store/useGreenBonusStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useSpinWheelStore from '@/store/useSpinWheelStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { getAccessToken } from '@/utils/helper';
import {
  chatRoomSocket,
  livePlayerSocket,
  playerActivitySocket,
  walletSocket,
} from '@/utils/socket';
import useAudioPlayer from '@/utils/useAudioPlayer';
import AgoraRTC from 'agora-rtc-sdk-ng';
import Image from 'next/image';
import Link from 'next/link';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import chat from '../../assets/icons/chat.png';
import brandLogo from '../../assets/images/logo/brand-logo.svg';
import { rtc as privateCallRTC } from '../../hooks/usePrivateCall';
import Auth from '../Auth';
import ChatHeader from '../ChatWindow/ChatHeader';
import PrimaryButton from '../Common/Button/PrimaryButton';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import CallPopup from '../Models/CallPopup';
import GroupCallPopup from '../Models/GroupCallPopup';
import WalletMenu from '../WalletMenu';
import useGroupChatStore from '@/store/useGroupChatStore';
function Header() {
  const {
    coin,
    setCoin,
    isAuthenticated,
    setUserDetails,
    setUserWallet,
    userWallet,
    userDetails,
  } = useAuthStore((state) => state);
  const router = useRouter();
  const { gameId } = useParams();
  const { setSection } = useChatSection();
  const { clearModals, openModal } = useModalStore((state) => state);
  const {
    closeModal: closeCallModal,
    openModal: openCallModal,
    setIsMinimized,
  } = useCallModalStore((state) => state);
  const { refetchPublicUsers, refetchMyFriends } = useActivePlayers();
  const { refetchPublicGroups, refetchMyGroups } = useActiveGroups();
  const { setConnectedPlay } = useActiveGroupStore();
  const { setGreenBonusData } = useGreenBonusStore((state) => state);
  const { audio, playAudio } = useAudioPlayer();
  const { setCallStartTime, setCallDuration, setToggleMuted } = useCallStore();
  const { setShowHomePopup } = useGeneralStore((state) => state);
  const { clearUserAuth, logout } = useHelperHook();
  const [isMobile, setIsMobile] = useState(false);
  // const [showChatHeader, setShowChatHeader] = useState(false)
  const [showChatIcon, setShowChatIcon] = useState(true);
  const { handleConnectedPlay } = useConnectedPlay();
  const {
    setShowChat,
    showChatHeader,
    setShowChatHeader,
    setChatHeaderActiveTab,
  } = useChatStore();

  const { setIFrameHeight } = useGameStore();
  const pathname = usePathname();
  console.log('🚀 ~ Header ~ pathname:', pathname);
  useEffect(() => {
    window.addEventListener('logout', async (event) => {
      // if (!localStorage.getItem('isAuthenticated')) {
      setSection('PublicChat');
      clearUserAuth();
      localStorage.clear();
      clearModals();
      router.push('/');
      // }
    });
  }, []);

  const rtc = {
    client: AgoraRTC.createClient({ mode: 'rtc', codec: 'h264' }),
    localAudioTrack: null,
    remoteAudioTrack: null,
  };

  const { setVoiceCall, voiceCall, updateGroupCallMembers } = useVoiceCallStore(
    (state) => state,
  );
  const { setSpinWheelData } = useSpinWheelStore((state) => state);

  const { selectedTab, setSelectedTab } = useAuthTab((state) => state);
  const accessToken = getAccessToken();
  const {
    openMenu,
    setOpeMenu,
    openChat,
    setOpenChat,
    setBanners,
    setActiveMenu,
    setToggleSideMenu,
  } = useGeneralStore((state) => state);
  const {
    isPrivateChatOpen,
    setIsPrivateChatOpen,
    isCallActive,
    setIsCallActive,
  } = usePrivateChatStore((state) => state);
  const {
    
    isCallActive:isGroupCallActive,
 
  } = useGroupChatStore((state) => state);

  const { data: bannersData } = useGetBannersQuery({ enabled: true });
  const [showWallet, setShowWallet] = useState(false);

  useEffect(() => {
    if (bannersData) setBanners(bannersData);
  }, [bannersData]);

  useEffect(() => {
    if (false) setSpinWheelData(spinWheelData?.wheelConfiguration);
  }, []);

  const toggleSwitch = () => {
    const newCoin = coin === 'SC' ? 'GC' : 'SC';
    console.log(newCoin, '::::::::newCoin');
    setCoin(newCoin);
  };

  const { data } = useUserProfileQuery({
    enabled: isAuthenticated,
  });

  useEffect(() => {
    if (data) setUserDetails(data);
  }, [data, setUserDetails]);

  // const audio = new Audio('/sounds/ringtone_call_phone.mp3');
  const onCoinUpdate = (data) => {
    console.log(data, '::::::::::::::::::::::::::::::on coin update socket 1');
    setUserWallet(data?.data);
  };
  const onChatRoomCall = (data) => {
    console.log(data, '::::::::123data');
    // openModal(<CallPopup />)
    setCallStartTime(null), setCallDuration(0);
    setToggleMuted(false);
    playAudio();
    openCallModal(<CallPopup />);
    setVoiceCall({
      channelName: data?.data?.channelName,
      role: data?.data?.role,
      callLogId: data?.data?.callLogId,
      userId: data?.data?.userId,
      username: data?.data?.username,
      profileImage: data?.data?.profileImage,
    });
  };

  const onGroupChatCall = (data) => {
    const isPrivateCallActive = usePrivateChatStore.getState().isCallActive;
    const isCallActive = useGroupChatStore.getState().isCallActive;
    console.log(
      '🚀 ~ onGroupChatCall ~ data:',
      data,
      userDetails,
      userDetails?.userMeta?.userId != data?.data?.userId,
      'aaaaaaa',
      isCallActive,
      // isGroupCallActive,
      useGroupChatStore.getState().isCallActive,
    );
    console.log(
      'iscall active',
      isCallActive,
      // isGroupCallActive,
      useGroupChatStore.getState().isCallActive,
    );
    if (isCallActive || isPrivateCallActive) {
      toast(` Group Call Started: ${data?.data?.groupName}`, {
        icon: '📞', // or 📞 or 🟢
        duration: 3000,
        style: {
          background: 'yellow', // info-blue
          color: 'black',
          fontWeight: '500',
        },
      });
      return;
    }
    setCallStartTime(null), setCallDuration(0);
    setToggleMuted(false);
    const currentUserId = userDetails?.userMeta?.userId ?? userDetails?.userId;

    if (currentUserId != data?.data?.userId) {
      console.log('iiiiiiiiiiii');
      playAudio();
    }

    openCallModal(<GroupCallPopup />);
    setIsMinimized(false);
    setVoiceCall({
      channelName: data?.data?.channelName,
      role: data?.data?.role,
      callLogId: data?.data?.callLogId,
      userId: data?.data?.userId,
      username: data?.data?.username,
      profileImage: data?.data?.profileImage,
      groupId: data?.data?.groupId,
      groupCallMembers:
        data?.data?.userId == userDetails?.userId
          ? [
              {
                username: userDetails?.username,
                profileImage: userDetails?.profileImage,
                firstName: userDetails?.firstName,
                lastName: userDetails?.lastName,
              },
            ]
          : data?.data?.members,
    });
  };

  const onGroupCallMembers = (data) => {
    updateGroupCallMembers([...data?.data]);
  };
  const onDeclineCall = async (data) => {
    const isCallActive = usePrivateChatStore.getState().isCallActive;

    try {
      audio.pause();
      audio.currentTime = 0;

      if (!isCallActive) {
        closeCallModal();
        return;
      }

      privateCallRTC.localAudioTrack?.close();
      await privateCallRTC.client.leave();
      usePrivateChatStore.getState().setIsCallActive(false); // call via getState
    } catch (error) {
      console.error('Error disconnecting call:', error);
    }
  };

  const onNotAttendedVoiceCall = async () => {
    const isCallActive = usePrivateChatStore.getState().isCallActive;
    try {
      if (!isCallActive) {
        closeCallModal();
        audio.pause();
        audio.currentTime = 0;
        return;
      }
      rtc.localAudioTrack?.close();
      await rtc.client.leave();
      console.log('iiiiiiiiiiiii1');
      setVoiceCall(null);
      setIsCallActive(false);
    } catch (error) {
      console.log(error, 'Error in not attended voice call');
    } finally {
      audio.pause();
      audio.currentTime = 0;
      // audio.pause();
      // audio.currentTime = 0;
    }
  };

  const onHandleActivePlayer = (data) => {
    refetchPublicUsers(), refetchMyFriends();
  };
  const onHandleActiveGroup = (data) => {
    console.log('🚀 ~ onHandleActiveGroup ~ data:', data);
    const voiceCall = useVoiceCallStore.getState().voiceCall;
    const isCallActive = useGroupChatStore.getState().isCallActive;

    refetchPublicGroups();
    refetchMyGroups();

    if (isCallActive && voiceCall?.groupId && voiceCall?.callLogId &&data?.data?.games?.length!=0) {
      handleConnectedPlay({
        groupId: voiceCall?.groupId,
        callLogId: voiceCall?.callLogId,
      });
    }
    console.log('Active Group socket data', data);
  };

  useEffect(() => {
    if (!rtc.client) return;

    rtc.client.on('user-published', async (user, mediaType) => {
      await rtc.client.subscribe(user, mediaType);
      if (mediaType === 'audio') {
        rtc.remoteAudioTrack = user.audioTrack;
        rtc.remoteAudioTrack.play();
      }
    });

    rtc.client.on('user-unpublished', () => {
      if (rtc.remoteAudioTrack) {
        rtc.remoteAudioTrack.stop();
        rtc.remoteAudioTrack = null;
      }
    });

    return () => {
      rtc.client.removeAllListeners();
    };
  }, []);

  const onRestrictUser = () => {
    setUserDetails({ ...userDetails, isRestrict: true });
    toast.error('You are restricted, Please contact administrator');
    router.push('/');
  };
  const onUnRestrictUser = () => {
    setUserDetails({ ...userDetails, isRestrict: false });
    toast.error('You are now unrestricted');
    router.push('/');
  };

  const onLogout = () => {
    // logout();
    console.log('its forced logged out????');
    window.dispatchEvent(new Event('logout'));
    // window.location.reload();
    // router.push('/');
    clearUserAuth();
    localStorage.clear();
    clearModals();
    router.push('/');
  };

  const onRemainingthresold = (data) => {
    setGreenBonusData(data.data);
  };

  useEffect(() => {
    if (isAuthenticated) {
      console.log(accessToken, ':::::::::::accessToken');

      // Set socket authentication
      walletSocket.auth = { token: accessToken };
      chatRoomSocket.auth = { token: accessToken };
      playerActivitySocket.auth = { token: accessToken };

      // Connect sockets
      walletSocket.connect();
      livePlayerSocket.connect();
      chatRoomSocket.connect();
      playerActivitySocket.connect();
      playerActivitySocket.on('connect', () => {
        console.log('Connected to player-activity socket');
      });

      playerActivitySocket.on('connect_error', (err) => {
        console.error('Connection error:', err.message);
      });
      // Define event listeners
      // const handleNotification = (data) => onNotificationDataHandler(data);
      const handleRestrictUser = (data) => onRestrictUser(data);
      const handleRemainingThreshold = (data) => onRemainingthresold(data);
      // const handleClaimGreenBonus = (data) => isClaimGreenBonusCalled(data);
      const handleUnrestrictUser = (data) => onUnRestrictUser(data);
      const handleCoinUpdate = (data) => onCoinUpdate(data);
      const handleLogout = (data) => onLogout(data);
      const handleChatRoomCall = (data) => onChatRoomCall(data);
      const handleGroupChatCall = (data) => onGroupChatCall(data);
      const handleDeclinePersonalCall = (data) => onDeclineCall(data);
      const handleNotAttendedVoiceCall = (data) => onNotAttendedVoiceCall(data);
      const handleGroupGroupMembers = (data) => onGroupCallMembers(data);
      const handleActivePlayer = (data) => onHandleActivePlayer(data);
      const handleActiveGroup = (data) => onHandleActiveGroup(data);
      // Register walletSocket event listeners
      // walletSocket.on('NOTIFICATION', handleNotification);
      walletSocket.on('USER_RESTRICT', handleRestrictUser);
      walletSocket.on('REMAINING_THRESHOLD', handleRemainingThreshold);
      walletSocket.on('CLAIM_GREEN_BONUS');
      walletSocket.on('USER_UNRESTRICT', handleUnrestrictUser);
      walletSocket.on('USER_WALLET_BALANCE', handleCoinUpdate);
      walletSocket.on('USER_BAN', handleLogout);
      walletSocket.on('USER_LOGOUT', handleLogout);

      // Register chatRoomSocket event listeners
      chatRoomSocket.on('PERSONAL_VOICE_CHAT_CHANNEL', handleChatRoomCall);
      chatRoomSocket.on('GROUP_VOICE_CHAT_CHANNEL', handleGroupChatCall);
      chatRoomSocket.on(
        'DECLINE_PERSONAL_VOICE_CHAT_CHANNEL',
        handleDeclinePersonalCall,
      );
      chatRoomSocket.on('NOT_ATTENDED_VOICE_CALL', handleNotAttendedVoiceCall);
      chatRoomSocket.on('GROUP_VOICE_CHAT_MEMBER', handleGroupGroupMembers);
      playerActivitySocket.on('MY_FRIENDS_PLAYER_ACTIVITY', handleActivePlayer);
      playerActivitySocket.on('PUBLIC_PLAYER_ACTIVITY', handleActiveGroup);

      // Cleanup function: Remove event listeners and disconnect sockets
      return () => {
        // Remove walletSocket listeners
        walletSocket.off('USER_RESTRICT');
        walletSocket.off('REMAINING_THRESHOLD');
        walletSocket.off('CLAIM_GREEN_BONUS');
        walletSocket.off('USER_UNRESTRICT');
        walletSocket.off('USER_WALLET_BALANCE');
        walletSocket.off('USER_BAN');
        walletSocket.off('USER_LOGOUT');

        // Remove chatRoomSocket listeners
        chatRoomSocket.off('PERSONAL_VOICE_CHAT_CHANNEL');
        chatRoomSocket.off('GROUP_VOICE_CHAT_CHANNEL');
        chatRoomSocket.off('DECLINE_PERSONAL_VOICE_CHAT_CHANNEL');
        chatRoomSocket.off('NOT_ATTENDED_VOICE_CALL');
        chatRoomSocket.off('GROUP_VOICE_CHAT_MEMBER');

        playerActivitySocket.off('MY_FRIENDS_PLAYER_ACTIVITY');
        playerActivitySocket.off('PUBLIC_PLAYER_ACTIVITY');

        // Disconnect sockets (optional, depending on your logic)
        walletSocket.disconnect();
        livePlayerSocket.disconnect();
        chatRoomSocket.disconnect();
        playerActivitySocket.disconnect();
      };
    }
  }, [isAuthenticated, accessToken]); // Add accessToken if it can change

  useEffect(() => {
    // setIFrameHeight('calc(100vh - 80px)');
    if (pathname?.split('/')?.includes('originals')) {
      const handleMessage = (event) => {
        const { eventName, data } = event.data || {};

        if (eventName === 'GAME_CONTAINER_HEIGHT') {
          console.log(
            'Received GAME_CONTAINER_HEIGHT:',
            data?.gameContainerHeight,
          );
          setIFrameHeight(`${data?.gameContainerHeight}px`);
        }
      };

      window.addEventListener('message', handleMessage);

      // Cleanup
      return () => {
        window.removeEventListener('message', handleMessage);
      };
    } else {
      if (window?.innerWidth < 1024) {
        setIFrameHeight('calc(100vh - 80px) ');
      } else {
        setIFrameHeight('calc(100vh - 140px)');
      }
    }
  }, [pathname]);
  useEffect(() => {
    const handleResize = () => {
      const isWideScreen = window.matchMedia('(min-width: 1280px)').matches;
      const laptop = window.matchMedia('(min-width: 1024px)').matches;
      setShowChatIcon(!isWideScreen);
      setOpenChat(isWideScreen);
      setOpenChat(laptop);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [setOpenChat]);

  const spring = {
    type: 'spring',
    stiffness: 700,
    damping: 30,
  };
  return (
    <>
      {showChatHeader && (
        <div
          className={`header-blur fixed left-0 top-0 z-[49] mb-10 flex h-[3.75rem] w-full items-center justify-between bg-oxfordBlue-1000  px-3 transition-all duration-300 ease-in-out lg:z-40  lg:ml-[14.75rem] lg:mr-[0rem] lg:w-[calc(100%-14.75rem-0rem)] lg:shadow-header`}
        >
          <ChatHeader setShowChatHeader={setShowChatHeader} />
        </div>
      )}
      <header
        className={`${showChatHeader ? 'hidden' : ''} ${isAuthenticated ? '' : '!z-[42]'} ${openChat ? 'lg:mr-[20.5rem] lg:w-[calc(100%-14.75rem-20.5rem)]' : 'lg:mr-[0rem] lg:w-[calc(100%-14.75rem-0rem)]'} header-blur fixed left-0 top-0 z-[52] mb-10 flex h-[3.75rem] w-full items-center  justify-between bg-oxfordBlue-1000 px-3 transition-all duration-300  ease-in-out lg:z-40 lg:ml-[14.75rem] lg:pr-12 lg:shadow-header`}
      >
        <Link href="/" className="inline-block lg:hidden">
          <Image
            src={brandLogo}
            width={100}
            height={100}
            className="min-w-[5rem] max-w-[5rem]"
            onClick={() => {
              if (window && window.innerWidth < 946) {
                setActiveMenu('/');
                setOpenChat(false);
              }
            }}
          />
        </Link>

        <div className=" relative m-[10px] w-full max-w-[62dvw] sm:ml-0 lg:hidden">
          {isAuthenticated && gameId && <GreenbarBonus />}
        </div>
        {!isAuthenticated && (
          <div className="flex grow items-center justify-end">
            <div className="flex w-full items-center justify-end gap-2">
              <PrimaryButtonOutline
                className="max-md:min-h-8 max-md:px-4 max-md:text-sm"
                onClick={() => {
                  localStorage.setItem('activeTab', 0);
                  setSelectedTab(0);
                  openModal(<Auth />);
                }}
              >
                Login
              </PrimaryButtonOutline>
              <PrimaryButton
                className="max-md:min-h-8 max-md:px-4 max-md:text-sm"
                onClick={() => {
                  localStorage.setItem('activeTab', 1);
                  setSelectedTab(1);
                  openModal(<Auth />);
                }}
              >
                Register
              </PrimaryButton>
            </div>
          </div>
        )}
        <div className="flex items-center gap-2">
          <div className="relative inline-block lg:hidden">
            {isAuthenticated && (
              <button
                onClick={() => {
                  setShowWallet((prev) => !prev);
                  setOpeMenu(false);
                }}
                className="flex h-7 w-7 items-center justify-center"
                type="button"
              >
                <WalletIcon className="h-7 w-7" />
              </button>
            )}
            {showWallet && <WalletMenu showWallet={showWallet} />}
          </div>

          {showChatIcon && (
            <button
              className="flex h-7 w-7 items-center justify-center lg:hidden"
              onClick={() => {
                console.log('its clicked');
                setShowHomePopup(true);
                setOpeMenu(false);
                setShowWallet(false);
                setShowChatHeader(!showChatHeader);
                setChatHeaderActiveTab('lobby');
              }}
              type="button"
            >
              <Image
                src={chat}
                className="h-7 w-7 transition-all duration-300"
              />
            </button>
          )}

          <button
            type="button"
            onClick={() => {
              setOpeMenu(!openMenu);
              setToggleSideMenu(false);
              setShowWallet(false);
            }}
            className="flex h-7 w-7 items-center justify-center lg:hidden"
          >
            <img
              src="https://flatironsmineralclub.org/wp-content/themes/fmc/img/menu_icon.png"
              alt="Menu"
              className="h-7 w-7 object-contain"
            />
          </button>
        </div>
      </header>
    </>
  );
}

export default Header;
