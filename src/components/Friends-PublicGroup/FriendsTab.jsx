import CallGoldenIcon from '@/assets/icons/CallGoldenIcon';
import CallRedIcon from '@/assets/icons/CallRedIcon';
import MessageGoldenIcon from '@/assets/icons/MessageGoldenIcon';
import usePrivateCall from '@/hooks/usePrivateCall';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import { useOpenChatWindow } from '@/utils/chat';
import { getAccessToken, slugify } from '@/utils/helper';
import { chatRoomSocket, voiceCallConnected } from '@/utils/socket';
import AgoraRTC from 'agora-rtc-sdk-ng';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import ChatAvatar from '../ChatWindow/ChatAvatar';
import MainLoader from '../Common/Loader/MainLoader';

const FriendsTab = ({ users, isLoading }) => {
  const {
    setIsPrivateChatOpen,
    setUserId,
    isCallActive,
    setIsCallActive,
    userId,
    searchUserName,
    setSearchUserName,
    callId,
  } = usePrivateChatStore((state) => state);
  const { initiateCall, disconnectCall, handleDisconnectCall } =
    usePrivateCall();
  // AgoraRTC.setLogLevel(4);

  const accessToken = getAccessToken();
  const openChatWindow = useOpenChatWindow();
  const router = useRouter();
  useEffect(() => {
    chatRoomSocket.auth = { token: accessToken };
    chatRoomSocket.connect();
    voiceCallConnected.auth = { token: accessToken };
    voiceCallConnected.connect();
    // chatRoomSocket.on('DECLINE_PERSONAL_VOICE_CHAT_CHANNEL', disconnectCall);
    // chatRoomSocket.on('NOT_ATTENDED_VOICE_CALL', disconnectCall);
  }, []);
  const openChat = (userId) => {
    setUserId(userId);
    setIsPrivateChatOpen(true);
    if (searchUserName != '') {
      setSearchUserName('');
    }
    openChatWindow();
  };
  const userStatusColor = (status) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-[#28a745] shadow-[0_0_5px_rgba(40,167,69,0.5)]'; // Green with subtle glow
      case 'RECENTLY_ACTIVE':
        return 'bg-[#ffc107] shadow-[0_0_5px_rgba(255,193,7,0.5)]'; // Yellow with subtle glow
      case 'BUSY':
        return 'bg-[#ffa500] shadow-[0_0_5px_rgba(255,193,7,0.5)]'; // Yellow with subtle glow
      case 'AWAY_MODE':
        return 'bg-[#f8f9fa] border border-[#ced4da] shadow-[0_0_5px_rgba(0,0,0,0.2)]'; // Light gray with a slight shadow
      case 'GHOST_MODE':
        return 'bg-[#ff0000] shadow-[0_0_5px_rgba(108,117,125,0.5)]'; // Dark gray with a subtle glow
      default:
        return 'bg-gray-500'; // Default color in case of unknown status
    }
  };
  if (isLoading) {
    return (
      <div className="text-md mt-6 flex h-[50dvh] items-center justify-center py-6 text-center text-gray-400">
        <MainLoader className="w-32" />
      </div>
    );
  }
  if (users?.rows?.length == 0) {
    return (
      <div className="text-md mt-6 flex h-[50dvh] items-center justify-center py-6 text-center text-gray-400">
        No friends found
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-2 p-0 pt-5 sm:grid-cols-2 md:gap-4 md:p-5 lg:grid-cols-3 ">
      {users?.rows?.map((user, index) => (
        <div
          key={user.userId + index}
          className="text-white relative flex flex-col rounded-lg bg-gray-700 p-4 shadow-md"
        >
          {/* User Info Section */}
          <div className="flex items-center gap-3">
            {/* Profile Image */}
            <div className="relative h-12 w-12 flex-shrink-0">
              <ChatAvatar
                profileImage={user?.relationUser?.profileImage}
                firstName={user?.relationUser?.firstName}
                lastName={user?.relationUser?.lastName}
                imageClassName="h-full w-full rounded-full object-cover"
                imageWidth={48}
                imageHeight={48}
                avatarSize={48}
              />
              <div
                className={`absolute bottom-0 right-[-4px] z-30 h-3 w-3 rounded-full ${userStatusColor(user?.relationUser?.currentStatus)}`}
              />
            </div>

            {/* Username */}
            <div className="min-w-0 flex-1">
              <div className="truncate text-lg font-bold">
                {user?.relationUser?.username}
              </div>
            </div>

            {/* Call & Message Icons */}
            <div className="flex flex-shrink-0 gap-3">
              {isCallActive ? (
                callId == user?.relationUser?.userId && (
                  <CallRedIcon
                    className="h-5 w-5  cursor-pointer"
                    onClick={() => {
                      if (isCallActive) {
                        handleDisconnectCall(user?.relationUser?.userId);
                      } else {
                        initiateCall(user?.relationUser?.userId);
                      }
                    }}
                  />
                )
              ) : (
                <CallGoldenIcon
                  className={`h-5 w-5 ${isCallActive ? 'text-red-600' : 'text-blue-400'}  cursor-pointer`}
                  onClick={() => {
                    if (isCallActive) {
                      handleDisconnectCall(user?.relationUser?.userId);
                    } else {
                      initiateCall(user?.relationUser?.userId);
                    }
                  }}
                />
              )}
              <MessageGoldenIcon
                className="h-5 w-5 cursor-pointer text-blue-400"
                onClick={() => openChat(user?.relationUserId)}
              />
            </div>
          </div>

          {/* Game Details Section */}
          {user?.relationUser?.currentGamePlay && (
            <div className="mt-2 w-full rounded-lg bg-[#808080ab] px-2 py-1">
              <div className="flex flex-wrap items-center gap-3">
                {/* Game Thumbnail */}
                <div className="flex h-16 w-20 shrink-0 items-center justify-center rounded-md bg-gray-500">
                  <Image
                    src={
                      user?.relationUser?.currentGamePlay?.gameThumbnail || ''
                    }
                    width={80}
                    height={64}
                    alt="Game image"
                    className="h-full w-auto rounded-md object-contain"
                  />
                </div>

                {/* Game Name & Button */}
                <div className="min-w-[140px] flex-1">
                  <p className="truncate">
                    {user?.relationUser?.currentGamePlay?.gameName}
                  </p>
                  <div className="mt-2 flex justify-end sm:justify-end">
                    <button
                      className="text-white w-full cursor-pointer rounded bg-gray-600 px-3 py-1 text-sm sm:w-auto"
                      onClick={() =>
                        router.push(
                          `/game/${slugify(user?.relationUser?.currentGamePlay?.moreDetails?.product)}/${slugify(user?.relationUser?.currentGamePlay?.gameName)}`,
                        )
                      }
                    >
                      Play game
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default FriendsTab;
