'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import useAuthStore from '@/store/useAuthStore';
import useHelperHook from '@/hooks/useHelperHook';
import useModalStore from '@/store/useModalStore';
import AuthCharacterSignup from '../../assets/images/stock-images/auth-character-signup.png';
import AuthCharacterSignin from '../../assets/images/stock-images/auth-character-login.png';
import SignUp from './SignUp';
import SignIn from './SignIn';
import Tabs from '../Common/Tabs';
import { useRouter } from 'next/navigation';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import IconButton from '../Common/Button/IconButton';
import useAuthTab from '@/store/useAuthTab';
import { useQueryClient } from '@tanstack/react-query'; 
function Auth() {
  const { isAuthenticated, hasRehydrated } = useAuthStore((state) => state);
  const { setUserDetails } = useAuthStore((state) => state);
  const { clearUserAuth } = useHelperHook();
  const { clearModals } = useModalStore((state) => state);
  const {selectedTab,setSelectedTab} = useAuthTab(state => state) 
  const [isForgotPassword, setIsForgotPassword] = useState(false);
  
    const queryClient = useQueryClient(); 
  // const [selectedTab, setSelectedTab] = useState(0);
    const router = useRouter();
  const tabs = [
    
    {
      label: 'Sign In',
      content: <SignIn isForgotPassword={isForgotPassword} setIsForgotPassword={setIsForgotPassword}/>,
    },
    {
      label: 'Sign Up',
      content: <SignUp />,
    }
  ];
  useEffect(() => {
    if (!isAuthenticated) {
      setUserDetails([]);
    }
  }, [isAuthenticated]);



  // useEffect(() => {
  //   if (localStorage.getItem('isAuthenticated') === 'true') {
  //     setIsAuthenticated(true);
  //   }
  // }, []);

  useEffect(() => {
    window.addEventListener('logout', async(event) => {
      // if (!localStorage.getItem('isAuthenticated')) {
      clearUserAuth();
      localStorage.clear();
      // queryClient.clear(); 
      clearModals();
      router.push('/')
      // }
    });
    console.log(localStorage.getItem('activeTab'), ":: localStorage.getItem('activeTab') ::::::", typeof localStorage.getItem('activeTab'))
    setSelectedTab(Number(localStorage.getItem('activeTab')) || 0);
  }, []);

  if (!hasRehydrated) {
    return null;
  }

  console.log(selectedTab,":::::::::::::selectedTab")

  const handleCloseModal = ()=>{
    clearModals()
  }
  return !isAuthenticated ? (
    <div
      id="authentication-modal"
      tabIndex="-1"
      aria-hidden="true"
      className="fixed left-0 right-0 top-0 z-50 flex h-[calc(100%)] max-md:h-[calc(100dvh_-_60px)] max-h-full w-full items-center justify-center overflow-y-auto overflow-x-hidden bg-black-850 md:inset-0"
    >
      <div className="relative max-h-full min-h-[31.688rem] w-full max-w-[53.25rem] p-4">
        <div className="relative min-h-auto overflow-hidden rounded-[0.625rem] bg-cetaceanBlue-1000 bg-auth-bg-desktop bg-cover bg-center bg-no-repeat md:bg-auth-bg-desktop">
        <div className="absolute right-3 top-3">
              <IconButton
                onClick={handleCloseModal}
                className="h-6 w-6 min-w-6"
              >
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          <div className="flex min-h-[inherit] justify-center px-5 py-6 max-md:pb-10">
            <div className="flex w-full justify-between max-md:flex-col max-md:items-center">
           
          
              <div className="w-full max-w-[30.25rem] p-4 max-sm:px-0 [&>.tabsContent]:mt-4">
                <Tabs classes='[&>ul>li>button]:font-bold' tabs={tabs} setSelectedTab={setSelectedTab} selectedTab={selectedTab} isVisible={!isForgotPassword}/>
              </div>
              <div className="my-auto w-full max-w-[18rem] demoUser">
                {selectedTab === 0 ? (
                  <Image
                    src={AuthCharacterSignup}
                    width={1000}
                    height={1000}
                    className="w-full max-w-[18rem]"
                    alt="Brand Logo"
                  />
                ) : (
                  <Image
                    src={AuthCharacterSignin}
                    width={1000}
                    height={1000}
                    className="w-full max-w-[18rem]"
                    alt="Brand Logo"
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  ) : null;
}

export default Auth;
