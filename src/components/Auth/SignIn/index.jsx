'use client';

import React, { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import useSignIn from '@/hooks/useSignIn';
import useAuthStore from '@/store/useAuthStore';
import InputField from '@/components/Common/InputField';
import { signInSchema } from '@/schemas/auth';
import ForgotPassword from '../ForgotPassword';
import { useTwoFactorVerifyLoginOtp } from '@/reactQuery/authQuery';
import Link from 'next/link';
import FacebookIcon from '@/assets/icons/FacebookIcon';
import TwitterIcon from '@/assets/icons/TwitterIcon';
import DiscordIcon from '@/assets/icons/DiscordIcon';
import WhatsappIcon from '@/assets/icons/WhatsappIcon';
import GoogleIcon from '@/assets/icons/GoogleIcon';
import OnlyFans from '@/assets/icons/onlyfansIcon.svg';
import Image from 'next/image';
import ForcedEmailModal from '@/components/Models/ForcedEmailModal';
import { useSearchParams } from 'next/navigation';
import { usePathname } from 'next/navigation'
import { useGoogleLogin } from "@react-oauth/google";
import TwitchIcon from '@/assets/icons/TwitchIcon';

function SignIn({ isForgotPassword, setIsForgotPassword }) {
  const [formData, setFormData] = useState({
    userName: '',
    password: '',
  });
  const [formErrors, setFormErrors] = useState({
    userName: '',
    password: '',
    otp: '',
  });
  const { signIn, isLoading, error, googleMutation, facebookMutation,discordMutation,twitchMutation } = useSignIn();
  const { setIsAuthenticated, userDetails, setUserDetails } = useAuthStore((state) => state);
  const [otp, setOtp] = useState();
  const [showTooltip, setShowTooltip] = useState(false);
  const searchParams = useSearchParams();
  const pathname = usePathname()


  useEffect(() => {
    // Load the Facebook SDK
    window.fbAsyncInit = function () {
      FB.init({
        appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
        cookie: true,
        xfbml: true,
        version: "v11.0",
      });
      FB.AppEvents.logPageView();
    };

    (function (d, s, id) {
      var js,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.src = "https://connect.facebook.net/en_US/sdk.js";
      fjs.parentNode.insertBefore(js, fjs);
    })(document, "script", "facebook-jssdk");
  }, []);

    useEffect(() => {
      const code = searchParams.get('code');
      let timeout;
      if (code) {
        timeout = setTimeout(() => {
          if (pathname === "/discord-login") {
            let payload = {
              code: code,
              isSignup: false,
            };
            discordMutation.mutate(payload);
          } else if (pathname === "/twitch-signup") {
            let payload = {
              code: code,
              isSignup: false,
            };
            twitchMutation.mutate(payload);
          }
        }, 500); // 500ms delay to prevent quick re-triggers
      }
    
      return () => clearTimeout(timeout);
    }, [pathname, searchParams]);



  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("its clicked!!!")
    try {
      signInSchema.parse({
        userName: formData.userName,
        password: formData.password,
      });
      signIn(formData.userName, formData.password);
    } catch (validationError) {
      setFormErrors(validationError.formErrors.fieldErrors);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    setFormErrors({
      ...formErrors,
      [name]: '',
    });
  };

  const handleForgotPasswordClick = () => {
    setIsForgotPassword(true);
  };

  const handleBackToSignInClick = () => {
    setIsForgotPassword(false);
    setFormData({ userName: '', password: '' });
    setFormErrors({ userName: '', password: '' });
  };

  const mutationVerfiy = useTwoFactorVerifyLoginOtp({
    onSuccess: (response) => {
      setOtp('');
      setIsAuthenticated(true);
      localStorage.setItem('isAuthenticated', true);
      toast.success(response.data.message);
      setUserDetails(response?.data?.user);
    },
    onError: (error) => {
      const message = error.response?.data?.errors?.[0]?.description;
      if (message) {
        console.log('error**', error);
        toast.error('Please a enter a valid otp');
      }
    },
  });

  const verfiyOtp = () => {
    if (otp) {
      mutationVerfiy.mutate({
        token: otp,
        email: userDetails?.user?.email,
        isMobile: false,
      });
      setFormErrors({ userName: '', password: '' });
    } else {
      setFormErrors({ ...formErrors, otp: 'Please enter a valid otp' });
    }
  };


  const handleFacebookClick = (e) => {
    e.preventDefault();
    e.stopPropagation()

    FB.login(
      function (response) {
        if (response && response.authResponse && response.authResponse.userID) {
          FB.api(
            `/${response.authResponse.userID}`,
            { fields: ["first_name", "last_name", "email"] },
            function (_response) {
              responseFacebook(_response);
            }
          );
        }
      },
      { scope: "public_profile,email" }
    );
  };

  const responseFacebook = (response) => {
    const userData = {
      firstName: response.first_name,
      lastName: response.last_name,
      userId: response.id,
      email: response.email,
      isSignup: false,
      isTermsAccepted: true,

      isForceEmail: false,
    };

    if (response && response.email) {
      facebookMutation.mutate(userData);
    } else {
      openModal(
        <ForcedEmailModal
          userData={userData}
        />
      );
    }
  };

  const signInWithDiscord = () => {


    const discordAuthUrl = `https://discord.com/oauth2/authorize?client_id=${process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID}&response_type=code&redirect_uri=${encodeURIComponent(process.env.NEXT_PUBLIC_DISCORD_REDIRECT_URI)}&scope=${process.env.NEXT_PUBLIC_DISCORD_SCOPE}`;

    const width = 500;
    const height = 600;
    const left = (window.innerWidth - width) / 2;
    const top = (window.innerHeight - height) / 2;

    window.open(
      discordAuthUrl,
      '_self',
      `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes,status=yes`
    );
  };

  const signInWithTwitch = () => {
    const clientId = process.env.NEXT_PUBLIC_TWITCH_CLIENT_ID;
    const redirectUri = process.env.NEXT_PUBLIC_TWITCH_REDIRECT_URI;
    const scope = 'openid user:read:email user:read:follows';
    console.log(redirectUri, ":::::::redirectUri")
    const authUrl = `https://id.twitch.tv/oauth2/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=${scope}`;

    window.location.href = authUrl;
  };

    const handleGoogleLogin = useGoogleLogin({
      onSuccess: (tokenResponse) => {
        if (tokenResponse) {
          const userData = {
            credential: tokenResponse.access_token,
            isSignup: false,
            isTermsAccepted: true,
          };
          googleMutation.mutate(userData);
        }
      },
      onError: (errorResponse) => console.log(errorResponse, ":::::google res login"),
    });
  


  return isForgotPassword ? (
    <ForgotPassword onBack={handleBackToSignInClick} />
  ) : (
    <div>
      <form onSubmit={handleSubmit}>
        <div className='grid grid-cols-1 gap-2'>
          <InputField
            type="text"
            name="userName"
            value={formData.userName}
            placeholder="Enter User Name"
            onChange={handleChange}
            error={formErrors.userName}
            label="User Name"
          />
          <InputField
            type="password"
            name="password"
            value={formData.password}
            placeholder="Enter Password"
            onChange={handleChange}
            error={formErrors.password}
            label="Password"
          />
        </div>

        <div className="mt-3 flex justify-end">
          <button
            type="button"
            onClick={handleForgotPasswordClick}
            className="ml-auto text-base font-bold leading-none text-steelTeal-1000 transition-all duration-300 hover:text-primary-1000"
          >
            Forgot Password?
          </button>
        </div>
        {!userDetails?.authEnable && (
          <div className="mt-6 flex justify-between flex-wrap md:justify-between max-xxs:flex-col gap-2 items-center w-full">
            <div className='flex items-center gap-2 pr-4 max-xxs:pr-0 max-xxs:flex-col'>
              <PrimaryButton type="submit" disabled={isLoading}>
                {isLoading ? 'Signing In...' : 'Sign In'}
              </PrimaryButton>
              <span>or</span>
            </div>
            <div className='flex items-center flex-wrap justify-end max-xxs:justify-center gap-2 [&>button]:bg-richBlack-1000 [&>button:hover]:bg-richBlack-500 [&>button]:h-10 [&>button]:w-10 [&>button]:flex [&>button]:items-center [&>button]:justify-center [&>button]:p-2 [&>button]:rounded-md [&>button]:transition-all [&>button]:duration-200 [&>button]:ease-in-out'>
              <button
                type="button"
                onClick={handleFacebookClick}

              ><FacebookIcon /></button>
              <button
                type="button"
                onClick={signInWithDiscord}

              ><DiscordIcon /></button>
              {/* twitch */}
              <button
                type="button"
                onClick={signInWithTwitch}

              ><TwitchIcon /></button>
              <button
                type="button"
                onClick={() => handleGoogleLogin()}
              >
                <GoogleIcon />
              </button>
              {/* <Link href='/'><GoogleIcon /></Link> */}
              {/* <button>
                <Image
                  src={OnlyFans}
                  width={10000}
                  height={10000}
                  className="h-5 w-5"
                />
              </button> */}
            </div>
          </div>
        )}
      </form>
      {userDetails?.authEnable && (
        <div>
          <div className="columns-1">
            <div className="mt-4 w-full ">
              <label className="mb-1 block flex gap-2 text-base font-normal text-steelTeal-1000">
                Two Factor Code{' '}
                <label
                  className="relative inline-block flex h-5 w-5 items-center justify-center rounded-full border bg-gray-700 text-white-1000"
                  onMouseEnter={() => setShowTooltip(true)}
                  onMouseLeave={() => setShowTooltip(false)}
                >
                  i
                  {showTooltip && (
                    <div className="text-white absolute bottom-full left-4  z-10 whitespace-nowrap rounded bg-gray-700 px-3 py-0.5 text-sm opacity-90">
                      Please check google authenticator for otp
                      {/* <div className="tooltip-arrow w-3 h-3 bg-gray-700 absolute transform -translate-x-1/2 translate-y-full"></div> */}
                    </div>
                  )}
                </label>
              </label>
              <div className="text-white relative w-full rounded-md ">
                <input
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  type="text"
                  placeholder="Enter a otp from google authenticatore"
                  className={`text-white w-full rounded-md border ${error
                    ? 'border-red-500'
                    : 'border-solid border-richBlack-1000'
                    } bg-richBlack-1000 p-3 text-base font-normal focus:border-steelTeal-1000`}
                />
                {formErrors?.otp && (
                  <label>
                    {otp?.length === 0
                      ? 'OTP is Required'
                      : otp.length < 6
                        ? formErrors?.otp
                        : ''}
                  </label>
                )}
              </div>
            </div>
          </div>
          <div className="mt-6 flex justify-center md:justify-start">
            <PrimaryButton
              type="submit"
              disabled={isLoading}
              onClick={verfiyOtp}
            >
              {isLoading ? 'Verifing Otp...' : 'Verify Otp'}
            </PrimaryButton>
          </div>
        </div>
      )}
    </div>
  );
}

export default SignIn;
