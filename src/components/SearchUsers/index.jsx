import SearchIcon from '@/assets/icons/Search';
import useUserInfoStore from '@/store/useUserInfoStore';
import { useGetUserTagsQuery } from '@/reactQuery/chatWindowQuery';
import useModalStore from '@/store/useModalStore';
import useAuthStore from '@/store/useAuthStore';
import useIgnoredUsers from '@/hooks/useIgnoredUsers';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import UserInfo from '../UserInfoModal';
import ChatAvatar from '../ChatWindow/ChatAvatar';

export default function SearchUsers(props) {
  // const [username, setUsername] = useState('');
  const { searchUserName: username, setSearchUserName: setUsername, setShowSearchInput, showSearchInput } =
    usePrivateChatStore();
  const { openUserInfoModal } = useUserInfoStore((state) => state);
  const { openModal } = useModalStore((state) => state);
  const { isAuthenticated } = useAuthStore((state) => state);

  const { data: searchResults } = useGetUserTagsQuery({
    enabled: !!username,
    params: {
      search: username,
    },
  });

  const onUserClick = (user) => {
    openUserInfoModal(user?.userId);
    openModal(
      <UserInfo
        setUsername={setUsername}
        setPrivateChatUserDetails={props.setPrivateChatUserDetails}
      />,
    );
  };
  const { ignoredUsers } = useIgnoredUsers();
  return (
    <div>
      {/* Search Input */}
      {showSearchInput ? <div className=" relative xl:mb-1 mt-[20px] xl:mt-[0rem] flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
        <input
          type="text"
          className={`h-10 w-full rounded-[0.625rem] ${
            showSearchInput ? 'border-2 border-primary-1000' : 'border-0'
          } bg-cetaceanBlue-1000 px-[0.520rem] py-2 transition-all duration-100 placeholder:text-steelTeal-1000 hover:border-primary-1000 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
          // className={`h-10 w-full rounded-[0.625rem] border-2 border-cetaceanBlue-1000 bg-cetaceanBlue-1000 px-[0.520rem] py-2 transition-all duration-100 placeholder:text-steelTeal-1000 hover:border-2 hover:border-primary-1000 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
          placeholder={isAuthenticated ? "Search users by username": "Login to search"}
          value={username}
          disabled={!isAuthenticated}
          onChange={(e) => setUsername(e.target.value)}
        />
        {/* Clear Icon */}
        {username && (
          <span
            className="absolute right-6 top-1/2 -translate-y-1/2 transform cursor-pointer text-gray-500 hover:text-gray-700"
            onClick={() => {setUsername(''); setShowSearchInput(false)}}
            style={{ fontSize: '16px' }}
          >
            &#x2715; {/* Unicode for a cross symbol (X) */}
          </span>
        )}
      </div>:
      <div className="hidden relative xl:mb-1 xl:mt-[0rem] lg:flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
        <SearchIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
        <input
          type="text"
          className={`h-10 w-full rounded-[0.625rem] border-2 border-cetaceanBlue-1000 bg-cetaceanBlue-1000 px-[0.520rem] py-2 transition-all duration-100 placeholder:text-steelTeal-1000 hover:border-2 hover:border-primary-1000 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
          placeholder={isAuthenticated ? "Search users by username": "Login to search"}
          value={username}
          disabled={!isAuthenticated}
          onChange={(e) => setUsername(e.target.value)}
        />
        {/* Clear Icon */}
        {username && (
          <span
            className="absolute right-6 top-1/2 -translate-y-1/2 transform cursor-pointer text-gray-500 hover:text-gray-700"
            onClick={() => {setUsername(''); setShowSearchInput(false)}}
            style={{ fontSize: '16px' }}
          >
            &#x2715; {/* Unicode for a cross symbol (X) */}
          </span>
        )}
      </div>
      }

      {/* Search Results */}
      {searchResults && searchResults?.length > 0 && (
        <section className="max-h absolute left-0 z-[1] h-screen w-full flex-1 overflow-y-auto bg-cetaceanBlue-1000 p-2">
          {searchResults?.length > 0 ? (
            <div className="flex flex-col gap-1 bg-black-850">
              {/* recentChats?.filter(d=>!( ignoredUsers?.rows?.map(row=>row?.relationUserId)?.includes( Number(d?.recipient_id)))) */}
              {searchResults
                ?.filter(
                  (d) =>
                    !ignoredUsers?.rows
                      ?.map((row) => row?.relationUserId)
                      ?.includes(Number(d.userId)),
                )
                ?.map((user) => (
                  <div
                    key={user.userId}
                    onClick={() => onUserClick(user)}
                    className="hover:bg-cetaceanBlue-800 flex cursor-pointer items-center gap-2 rounded-lg border border-oxfordBlue-1000 bg-maastrichtBlue-1000 px-2 py-1"
                  >
                    <div className="relative h-8 w-8 rounded-full border-2 border-oxfordBlue-1000">
                      <ChatAvatar
                        profileImage={user?.profileImage}
                        firstName={user?.firstName}
                        lastName={user?.lastName}
                        imageClassName="h-full w-full rounded-full object-cover"
                        imageWidth={32}
                        imageHeight={32}
                        avatarSize={32}
                      />
                    </div>
                    <span className="text-base leading-tight text-white-1000">
                      {user.username}
                    </span>
                  </div>
                ))}
            </div>
          ) : (
            <div className="mt-10 text-center text-white-1000">
              No users found
            </div>
          )}
        </section>
      )}
    </div>
  );
}
