import React, { useState, useEffect } from 'react';
import Select from 'react-select';
import Image from 'next/image';
import { useGetFriendsListQuery } from '@/reactQuery/chatWindowQuery';
import UserImg from '../../../public/assets/demo-image/group-img.jpg';
import UserAvatar from '../DefaultImage';

// Custom ValueContainer component
function CustomValueContainer({ children, ...props }) {
  const [values, input] = children || [[], null];

  return (
    <div className="chatMulti-inner-select__group w-full rounded-md bg-richBlack-1000">
      <div className='chatMulti-inner-select__input-container-section relative mx-0 mb-0 mt-0 h-[42px] w-full basis-full rounded-md border border-solid border-borderColor-100 bg-black-1000 px-9 before:absolute before:left-3 before:top-1/2 before:z-[1] before:h-5 before:w-5 before:-translate-y-1/2 before:rounded-md before:bg-chatSearchIcon before:bg-center before:bg-no-repeat before:content-[""]'>
        {input}
      </div>
      {values && values.length > 0 && (
        <div className="chatMulti-inner-select__multiOption flex flex-wrap gap-x-2 gap-y-1 p-4">
          {values}
        </div>
      )}
    </div>
  );
}

// Define the main component
function MultiSelect({
  className = '',
  classNamePrefix = '',
  onChange,
  value = [],
  isSearchable = true,
  placeholder = "Search and select friends..."
}) {
  const [searchTerm, setSearchTerm] = useState('');

  const { data: friendsList, isLoading, error } = useGetFriendsListQuery({
    enabled: true,
    params: { search: searchTerm },
  });

  const [selectedValues, setSelectedValues] = useState([]);

  useEffect(() => {
    if (value && value.length > 0 && friendsList?.rows) {
      const selectedOptions = friendsList.rows
        .filter(friend => value.includes(friend.relationUserId))
        .map(friend => ({
          value: friend.relationUserId,
          label: (
            <div className="flex items-center gap-1 ">
              {friend.relationUser.profileImage ? (
                <Image
                  src={friend.relationUser.profileImage}
                  alt={friend.relationUser.firstName}
                  className="h-4 w-4 rounded-full"
                  width="100"
                  height="100"
                />
              ) : (
                <UserAvatar
                  firstName={friend.relationUser.firstName}
                  lastName={friend.relationUser.lastName}
                  size={16}
                />
              )}
              <h6 className="pt-[3px] text-sm leading-none text-white-1000">
                {friend.relationUser.firstName} {friend.relationUser.lastName} - (
                {friend.relationUser.username})
              </h6>
            </div>
          ),
        }));
      setSelectedValues(selectedOptions);
    } else if (!value || value.length === 0) {
      setSelectedValues([]);
    }
  }, [value, friendsList]);

  const handleChange = (selectedOptions) => {
    // Extract only the `value` (user IDs) from the selected options
    const selectedUserIds = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    setSelectedValues(selectedOptions);
    onChange(selectedUserIds); // Pass the selected user IDs back to parent component
  };

  const dynamicOptions =
    friendsList && friendsList.rows
      ? friendsList.rows.map((friend) => ({
          value: friend.relationUserId,
          label: (
            <div className="flex items-center gap-1 ">
              {friend.relationUser.profileImage ? (
                <Image
                  src={friend.relationUser.profileImage}
                  alt={friend.relationUser.firstName}
                  className="h-4 w-4 rounded-full"
                  width="100"
                  height="100"
                />
              ) : (
                <UserAvatar
                  firstName={friend.relationUser.firstName}
                  lastName={friend.relationUser.lastName}
                  size={16}
                />
              )}
              <h6 className="pt-[3px] text-sm leading-none text-white-1000">
                {friend.relationUser.firstName} {friend.relationUser.lastName} - (
                {friend.relationUser.username})
              </h6>
            </div>
          ),
        }))
      : [];

  const handleInputChange = (inputValue) => {
    setSearchTerm(inputValue);
  };

  return (
    <Select
      closeMenuOnSelect={false}
      isMulti
      isSearchable={isSearchable}
      options={dynamicOptions}
      className={`${className}`}
      classNamePrefix={`${classNamePrefix}`}
      defaultMenuIsOpen={false}
      components={{
        ValueContainer: CustomValueContainer,
      }}
      value={selectedValues}
      onChange={handleChange}
      onInputChange={handleInputChange}
      placeholder={placeholder}
      filterOption={null}
      isLoading={isLoading}
      backspaceRemovesValue={false}
      noOptionsMessage={() =>
        isLoading ? "Loading friends..." :
        searchTerm ? `No friends found matching "${searchTerm}"` :
        "No friends available"
      }
    />
  );
}

export default MultiSelect;
