'use client';

import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import { motion, useAnimation } from 'framer-motion';
import { useQueryClient } from '@tanstack/react-query';
import HeartStrokeIcon from '@/assets/icons/Heart-Stroke';
import { useUpdateFavoriteMutation } from '@/reactQuery/gamesQuery';
import HeartFillIcon from '@/assets/icons/Heart-Fill';
import { toast } from 'react-hot-toast';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
import defaultImage from '../../../assets/images/png/default-image.png';

function GameCard({
  src,
  alt,
  width,
  height,
  onClick,
  gameId,
  isFavorite,
  aspectRatio = 'aspect-[164/222]',
}) {
  const queryClient = useQueryClient();
  const { isAuthenticated } = useAuthStore((state) => state);
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useModalStore((state) => state);
  const [isFavouriteGame, setIsFavouriteGame] = useState(isFavorite);
  const [imgSrc, setImgSrc] = useState(src);

  useEffect(() => {
    setImgSrc(src)
  }, [src])

  useEffect(() => {
    setIsFavouriteGame(isFavorite);
  }, [isFavorite]);
  const { mutate: updateFavorite, isPending } = useUpdateFavoriteMutation({
    onSuccess: (response, variables) => {
      if (variables.request) {
        toast.success('Game added to favorite');
        setIsFavouriteGame(true);
      } else {
        toast.success('Game removed from favorite');
        setIsFavouriteGame(false);
      }
      queryClient.invalidateQueries({ queryKey: ['GET_FAVORITES_GAMES'] });
      try {
        queryClient.setQueriesData(
          { queryKey: ['GET_SUB_CATEGORY_GAMES'] },
          (oldData) => {
            if (!oldData || !Array.isArray(oldData)) return oldData;
            return oldData.map(group => {
              if (!group || !Array.isArray(group.subCategoryGames)) return group;

              return {
                ...group,
                subCategoryGames: group.subCategoryGames.map(game =>
                  game?.masterCasinoGameId === gameId
                    ? { ...game, FavoriteGames: variables.request }
                    : game
                )
              };
            });
          }
        );
      } catch (error) {
        console.warn('Failed to update search cache:', error);
        queryClient.invalidateQueries({ queryKey: ['GET_SUB_CATEGORY_GAMES'] });
      }
      queryClient.invalidateQueries({ queryKey: ['GET_CUSTOM_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_SUBCATEGORY_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_ACTIVE_PLAYER_PUBLIC'] });
      queryClient.invalidateQueries({ queryKey: ['GET_ACTIVE_PLAYER_MY_FRIENDS'] });
    },
    onError: (error, variables) => {
      setIsFavouriteGame(!variables.request);
      toast.error('Something went wrong. Please try again.');
      console.error('Error updating favorite:', error);
    },
  });

  const toggleFav = () => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 0);
      setSelectedTab(0);
      openModal(<Auth />);
      return;
    }
    if (isPending) return;

    if (!gameId) {
      toast.error('Game ID is missing. Cannot update favorite.');
      console.error('GameId is missing:', { gameId, isFavorite, isFavouriteGame });
      return;
    }

    const updatedStatus = !isFavouriteGame;
    setIsFavouriteGame(updatedStatus);

    updateFavorite({ request: updatedStatus, gameId });
  };

  const controls = useAnimation();
  const handleHoverStart = () => {
    controls.start({
      y: [-6, 0, -4], // up, back down, up again
      scale: [1.05, 1, 1.03], // scale up, normal, scale up again
      transition: {
        duration: 0.7,
        ease: 'easeInOut',
      },
    });
  };

  const handleHoverEnd = () => {
    controls.start({
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut',
      },
    });
  };

  return (
    <motion.div
      className="relative overflow-hidden rounded-xl"
      whileHover={{
        boxShadow: '0px 4px 19.1px 0px #FFDB1D8C',
      }}
      onHoverStart={handleHoverStart}
      onHoverEnd={handleHoverEnd}
      whileTap={{ scale: 0.95 }}
      animate={controls}
    >
      <Image
        src={imgSrc}
        width={width}
        height={height}
        className={`${aspectRatio} h-full w-full max-w-full hover:cursor-pointer`}
        alt={alt}
        onClick={onClick}
        loading="eager"
        onError={(e) => {
          setImgSrc(defaultImage);
        }}
      />
      <button
        type="button"
        onClick={toggleFav}
        className="group absolute right-2 top-2 flex h-7 w-7 items-center justify-center rounded-lg bg-cetaceanBlue-1000 transition-all duration-300 active:scale-90"
      >
        {isFavouriteGame ? (
          <HeartFillIcon className="h-4 w-4 fill-primary-1000" />
        ) : (
          <>
            <HeartStrokeIcon className="h-4 w-4 fill-primary-1000 group-hover:hidden" />
            <HeartFillIcon className="hidden h-4 w-4 fill-primary-1000 group-hover:block" />
          </>
        )}
      </button>
    </motion.div>
  );
}

export default GameCard;
