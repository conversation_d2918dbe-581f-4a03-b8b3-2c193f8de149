'use client';

import FavoriteIcon from '@/assets/icons/Favorite';
import GCicon from '@/assets/icons/GCicon';
// import MyAccountIcon from '@/assets/icons/MyAccount';
// import ProfileIcon from '@/assets/icons/Profile';
import SCicon from '@/assets/icons/SCicon';
import SettingIcon from '@/assets/icons/Setting';
// import VIPIcon from '@/assets/icons/VIP';
import SpinWheelLottie from '@/assets/json/spin-wheel.json';
import useAuthStore from '@/store/useAuthStore';
import useGeneralStore from '@/store/useGeneralStore';
import useModalStore from '@/store/useModalStore';
import { formatValueWithK, getAccessToken } from '@/utils/helper';
import { walletSocket } from '@/utils/socket';
import Lottie from 'lottie-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import HamburgerSideMenu from '@/assets/icons/HamburgerSideMenu';
import FootballIcon from '@/assets/icons/Football';
import CasinoIcon from '@/assets/icons/Casino';
import ProfileGoldenIcon from '@/assets/icons/ProfileGoldenIcon';
import FavGoldenIcon from '@/assets/icons/FavGoldenIcon';
import VIPGolddenIcon from '@/assets/icons/VIPGoldenIcon';
import TransactionGoldenIcon from '@/assets/icons/TransactionGoldenIcon';
import SettingGoldenIcon from '@/assets/icons/SettingGoldenIcon';
import FriendsGoldenIcon from '@/assets/icons/FriendsGoldenIcon';
import BrandLogo from '../../assets/images/logo/brand-logo.svg';
import AnimatedButton from '../Common/Button/AnimatedButton';
import DropDown from '../Common/DropDown';
import LogoutButton from '../LogoutButton';
import SpinWheel from '../SpinWheel';
import StatusButton from '../StatusButton';
import ToggleSwitch from '../ToggleSwitch';
import TabButton from '../Tab';
import CasinoGoldenIcon from '@/assets/icons/CasinoGoldenIcon';
import GiftIcon from '@/assets/icons/GiftIcon';
import { useBonusDetails } from '@/reactQuery/gamesQuery';
import NoGBBCard from '../NoGBBCard';
import GBBCard from '../GBBCard';

function SideMenu() {
  const tabs = [
    {
      id: 'casino',
      label: 'Casino',
      href: '/',
      // icon: FavoriteIcon
    },
    {
      id: 'sports',
      label: 'Sports',
      href: '/sports',
      // icon: FavoriteIcon
    },
  ];
  const { openModal } = useModalStore((state) => state);
  useAuthStore();
  const popupRef = useRef(null);

  const { coin, isAuthenticated, setUserWallet, userWallet } = useAuthStore(
    (state) => state,
  );
  const pathname = usePathname();
  const accessToken = getAccessToken();
  const [showPopup, setShowPopup] = useState(false);

  const onCoinUpdate = (data) => {
    // console.log(data?.data, ':::::::::::::::::::data?.data');
    setUserWallet(data?.data);
  };
  const isGamePage = pathname.startsWith('/game/');
  // console.log(isGamePage, '::::::::::userWallet');

  useEffect(() => {
    walletSocket.auth = { token: accessToken };
    if (isAuthenticated) {
      walletSocket.connect();
      walletSocket.on('USER_WALLET_BALANCE', onCoinUpdate);
    }
  }, [isAuthenticated]);

  const profileMenuList = [
    {
      component: ProfileGoldenIcon,
      href: '/user',
      text: 'My Account',
    },
    // {
    //   component: SettingIcon,
    //   href: '/setting',
    //   text: 'Settings',
    // },
    // {
    //   component: NotificationIcon,
    //   text: 'Notice',
    //   button: true,
    //   onClick: () => openModal(<Notice />),
    // },
    {
      component: VIPGolddenIcon,
      text: 'VIP',
      href: '/vip',
      // button: true,
      // onClick: () => openModal(<Vip />),
    },
    {
      component: TransactionGoldenIcon,
      text: 'Game History',
      // button: true,
      // onClick: () => openModal(<TransactionsModal />),
      href: '/transactions',
    },
    {
      component: FriendsGoldenIcon,
      href: '/public-friends',
      text: 'Friends And Groups',
    },
    // {
    //   component: AffiliateIcon,
    //   href: '/affliate',
    //   text: 'Affliate',
    // },
    // {
    //   component: ShareIcon,
    //   href: '/share',
    //   text: 'Share',
    // },
  ];

  const menuList = [
    // {
    //   component: FootballIcon,
    //   href: '/sports',
    //   text: 'Sports',
    //   requiresAuth: true,
    // },
    {
      component: FavGoldenIcon,
      href: '/favorites',
      text: 'Favorites',
      requiresAuth: true,
    },
    // {
    //   component: TaskListIcon,
    //   href: '/task-list',
    //   text: 'Task List',
    // },
    // {
    //   component: FaucetIcon,
    //   text: 'Faucet',
    //   button: true,
    //   onClick: () => openModal(<Faucet />),
    // },
    // {
    //   component: ChestCardIcon,
    //   text: 'Chest and Card',
    //   button: true,
    //   onClick: () => openModal(<ChestAndCardModal />),
    // },

    // {
    //   component: FAQIcon,
    //   href: '/faq',
    //   text: 'FAQ',
    //   requiresAuth: false,
    // },
    // {
    //   component: FAQIcon,
    //   href: '/inventory',
    //   text: 'Inventory',
    // },
  ].filter((item) => !item.requiresAuth || isAuthenticated);

  const { openMenu, setOpeMenu, toggleSideMenu, setToggleSideMenu } =
    useGeneralStore((state) => state);

  // Function to close sidebar when navigation item is clicked
  const closeSidebar = () => {
    setOpeMenu(false);
  };

  const openSpinWheel = () => {
    openModal(<SpinWheel check />);
    closeSidebar();
  };
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setShowPopup(false);
      }
    };

    if (showPopup) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showPopup]);

  useEffect(() => {
    const handleResize = () => {
      if (window.visualViewport) {
        if (window?.innerWidth < 1280) {
          setToggleSideMenu(false);
        } else {
          setOpeMenu(false);
        }
      }
    };

    window.addEventListener('resize', handleResize);

    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const { refetch } = useBonusDetails({enabled:false});

  const handleClick = async () => {
    const result = await refetch();
    const bonusData = result?.data?.bonusData
    if(bonusData?.greenBonusDetails){
      openModal(<GBBCard bonusData={bonusData}/>);
    }
    else{
      openModal(<NoGBBCard/>);
    }
    closeSidebar();
  };

  return (
    <aside
      id="default-sidebar"
      className={`${
        openMenu
          ? 'right-0 w-[14.75rem] lg:left-0 flex flex-col gap-[20px]'
          : toggleSideMenu
          ? 'right-[-10%] w-16'
          : 'right-[-100%] w-[14.75rem]'
      } fixed top-[3.75rem] z-[50] h-[100dvh] bg-cetaceanBlue-1000 p-4 transition-all duration-300 ease-in-out lg:left-0 lg:top-0 lg:h-dvh`}
            aria-label="Sidebar"
    >
      <div className="flex gap-5">
        <button
          type="button"
          onClick={() => setToggleSideMenu(!toggleSideMenu)}
          className="ml-2  hidden h-6 lg:flex "
        >
          <div className="flex gap-5">
            <HamburgerSideMenu className="h-full w-full fill-white-750" />
          </div>
        </button>
        {!toggleSideMenu  && (
          <Link href="/">
            <Image
              src={BrandLogo.src}
              width={1000}
              height={1000}
              className="hidden lg:flex w-full max-w-[6.75rem]"
              alt="Brand Logo"
            />
          </Link>
        )}
      </div>
      {toggleSideMenu ? (
        <div className=" mt-8 flex flex-col justify-center gap-3">
          <div className="mb-3 rounded-lg bg-maastrichtBlue-1000 py-2.5 pl-[0.4rem] pr-8">
            <ToggleSwitch />
          </div>
         
            <>
              <div
                className="mb-3 rounded-lg bg-maastrichtBlue-1000 py-2.5 pl-[0.4rem] pr-3"
                title="Casino"
              >
                <Link href="/">
                  <CasinoGoldenIcon className="h-5 w-5 cursor-pointer fill-white-1000" />
                </Link>
              </div>
              <div
                className="mb-3 rounded-lg bg-maastrichtBlue-1000 py-2.5 pl-[0.4rem] pr-3"
                title="Sports"
              >
                <Link href="/sports">
                  <FootballIcon className="h-5 w-5 cursor-pointer fill-white-1000" />
                </Link>
              </div>
            </>

          {isAuthenticated && (
            <>
              <div
                className="mb-3 cursor-pointer rounded-lg bg-maastrichtBlue-1000 py-2.5 pl-[0.4rem] pr-3"
                ref={popupRef}
                title="Profile"
              >
                <button
                  type="button"
                  onClick={() => {
                    setShowPopup(!showPopup);
                  }}
                >
                  <ProfileGoldenIcon className="h-5 w-5 fill-white-1000" />
                </button>
                {showPopup && (
                  <ProfilePopUpSection
                    profileMenuList={profileMenuList}
                    onClose={() => setShowPopup(false)}
                  />
                )}
              </div>
              <div
                className="mb-3 rounded-lg bg-maastrichtBlue-1000 py-2.5 pl-[0.4rem] pr-3"
                title="Favorites"
              >
                <Link href="/favorites">
                  <FavGoldenIcon className="h-5 w-5 cursor-pointer fill-white-1000" />
                </Link>
              </div>
            </>
          )}
        </div>
      ) : (
        <>
          <div className="lg:mb-8 items-center justify-center lg:pt-10 flex">
              <TabButton tabs={tabs} defaultActiveTab="casino" onTabClick={closeSidebar} />
          </div>
          <div className="hidden lg:block mb-3 rounded-lg bg-maastrichtBlue-1000 px-3 py-2.5">
            <div className="relative flex flex-row-reverse items-center gap-1 overflow-hidden pb-4">
              <ToggleSwitch />

              <div className="flex w-full max-w-[152px] flex-col gap-1">
                <div
                  className={`relative flex h-8 items-center justify-between gap-2 rounded-3xl bg-primary-500 py-1.5 pl-5 pr-8 font-outfit text-base font-bold ${coin === 'GC' && 'grayscale'}`}
                >
                  {isAuthenticated &&
                    !isGamePage &&
                    (formatValueWithK(userWallet?.scCoin) || 0)}
                  <SCicon className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2" />
                </div>

                <div
                  className={`relative flex h-8 items-center justify-between gap-2 rounded-3xl bg-golden-500 py-1.5 pl-5 pr-8 font-outfit text-base font-bold ${coin === 'SC' && 'grayscale'}`}
                >
                  {isAuthenticated &&
                    !isGamePage &&
                    (formatValueWithK(userWallet?.gcCoin) || 0)}
                  <GCicon className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2" />
                </div>
              </div>
            </div>
            <AnimatedButton />
          </div>

          <div className="hidden items-center justify-center py-10">
            <div className="relative flex h-16 w-full cursor-pointer items-center justify-end rounded-lg bg-maastrichtBlue-1000 bg-snipWheelBackground">
              <Lottie
                animationData={SpinWheelLottie}
                loop
                className="absolute -top-5 left-5 w-24"
                onClick={openSpinWheel}
              />
              <div className="mr-5 w-16 text-2xl font-bold leading-6">
                Spin Wheel
              </div>
            </div>
          </div>
          {isAuthenticated && <div onClick={handleClick} className='h-[50px] flex items-center gap-2.5 bg-maastrichtBlue-1000 px-5 py-1 rounded-[10px] border border-solid border-green-400 cursor-pointer'><GiftIcon />  Bonus Rewards</div>}
          <div className="scrollbar-none h-full overflow-y-auto overflow-x-hidden rounded-[0.625rem] lg:h-[calc(100%_-_16rem)]">
            {isAuthenticated && (
              <div className="rounded-lg bg-maastrichtBlue-1000 px-1.5 py-1.5">
                <DropDown
                  toggle={true}
                  items={{
                    title: (
                      <div className="flex items-center justify-start gap-2.5">
                        <ProfileGoldenIcon className="h-5 w-5 fill-white-1000" />
                        <span className="text-base font-normal text-white-1000">
                          Profile
                        </span>
                      </div>
                    ),
                    content: (
                      <>
                        <DropDownContent contentList={profileMenuList} onClose={closeSidebar} />
                        <StatusButton onClose={closeSidebar} />
                        <LogoutButton onClose={closeSidebar} />
                      </>
                    ),
                  }}
                />
              </div>
            )}

            {menuList?.length >0 && <div className="mt-2.5 rounded-lg bg-maastrichtBlue-1000 px-1.5 py-1.5">
              <ul className="">
                <li>
                  <DropDownContent contentList={menuList} onClose={closeSidebar} />
                </li>
              </ul>
            </div>}
          </div>
        </>
      )}
    </aside>
  );
}

export default SideMenu;

function DropDownContent({ contentList, onClose }) {
  return contentList.map((item) => {
    const IconComponent = item?.component;
    if (item?.button) {
      return (
        <div
          role="button"
          key={item.text}
          onClick={() => {
            item.onClick();
            if (onClose) onClose();
          }}
          tabIndex={0}
          onKeyDown={() => {}}
          className="flex w-full items-center justify-start gap-2.5 rounded-[0.375rem] p-2.5 hover:bg-nav-gradient"
        >
          <IconComponent className="h-5 w-5 fill-steelTeal-1000" />
          <span className="text-base font-normal text-white-1000">
            {item.text}
          </span>
        </div>
      );
    }
    return (
      <Link
        key={item.href}
        href={item.href}
        onClick={() => {
          if (onClose) onClose();
        }}
        className="flex items-center justify-start gap-2.5 rounded-[0.375rem] p-2.5 hover:bg-nav-gradient"
      >
        <IconComponent className="h-5 w-5 fill-steelTeal-1000" />
        <span className="text-base font-normal text-white-1000">
          {item.text}
        </span>
      </Link>
    );
  });
}

function ProfilePopUpSection({ profileMenuList, onClose }) {
  return (
    <div className="relative">
      {/* Triangle pointer */}
      <div
        className="absolute left-[30px] top-[-13px]  z-20 h-3 w-3 bg-[#e5e7eb]"
        style={{
          clipPath: 'polygon(100% 0, 0 50%, 100% 100%)', // Adjust shape direction
        }}
      />

      {/* Dropdown content */}
      <div className="absolute left-[2.6rem] top-[-33px]  z-10 grid max-h-[50dvh] w-max rounded-md border border-[grey] bg-[#000000fa]">
        <DropDownContent contentList={profileMenuList} onClose={onClose} />
        <StatusButton onClose={onClose} />
        <LogoutButton onClose={onClose} />
      </div>
    </div>
  );
}
