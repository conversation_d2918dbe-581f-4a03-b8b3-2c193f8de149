import { create } from 'zustand';
// import { persist } from 'zustand/middleware';

const useGroupChatStore = create(
  // persist(
  (set) => ({
    groupChat: [],
    groupPageChat: [],
    groupId: null,
    userId: null,
    activeGroupPageTab: 'about',
    groupName: '',
    isGroupChatOpen: false,
    isGroupPageOpen: false,
    livePlayersCountInGroup: 0,
    recipientUser: null,
    isCallActive: false,
    groupCallDetails:{},
    toggleGroupTab:"MY_GROUPS",
    setLivePlayersCountInGroup: (data) => {
      set(() => ({ livePlayersCountInGroup: data }));
    },
    setIsCallActive: (data) => {
      set(() => ({ isCallActive: data }));
    },
    setIsGroupPageOpen: (data) => {
      set(() => ({ isGroupPageOpen: data }));
    },
    setActiveGroupPageTab: (data) => {
      set(() => ({ activeGroupPageTab: data }));
    },
    setIsGroupChatOpen: (data) => {
      if (data) {
        set(() => ({ isGroupChatOpen: data }));
      } else {
        set(() => ({
          groupChat: [],
          userId: null,
          // groupId:null,
          isGroupChatOpen: data,
          recipientUser: null,
        }));
      }
    },
    setUserId: (data) => {
      set(() => ({ userId: data }));
    },
    setGroupId: (data) => {
      set(() => ({ groupId: data }));
    },
    setGroupName: (data) => {
      set(() => ({ groupName: data }));
    },
    setRecipientUser: (data) => {
      set(() => ({ recipientUser: data }));
    },
    setGroupChat: (data) => {
      set(() => ({ groupChat: data?.slice().reverse() }));
    },
    setGroupPageChat: (data) => {
      set(() => ({ groupPageChat: data?.slice().reverse() }));
    },
    appendGroupChat: (message) => {
      set((state) => ({ groupChat: [...state.groupChat, message] }));
    },
    appendGroupPageChat: (message) => {
      set((state) => ({ groupPageChat: [...state.groupPageChat, message] }));
    },
      setGroupCallDetails: (data) => {
      set(() => ({ groupCallDetails: data }));
    },
      setToggleGroupTab: (data) => {
      set(() => ({ toggleGroupTab: data }));
    },
  }),
  //   {
  //     name: 'useGroupChatStore',
  //   },
  // ),
);

export default useGroupChatStore;
