import { create } from 'zustand';
// import { persist } from 'zustand/middleware';

const usePrivateChatStore = create(
  // persist(
  (set) => ({
    privateChat: [],
    userId: null,
    isPrivateChatOpen: false,
    recipientUser: null,
    isCallActive: false,
    searchUserName: '',
    showSearchInput: false,
    callId: null,
    channelName: '',
    toggleSearchInput: () =>
      set((state) => ({ showSearchInput: !state.showSearchInput })),
    setShowSearchInput: (value) => set({ showSearchInput: value }),
    setSearchUserName: (data) => {
      set(() => ({ searchUserName: data }));
    },
    setIsCallActive: (data) => {
      set(() => ({ isCallActive: data }));
    },
    setCallId: (data) => {
      set(() => ({ callId: data }));
    },
    setChannelName: (data) => {
      set(() => ({ channelName: data }));
    },

    setIsPrivateChatOpen: (data) => {
      console.log(data, ':::::::::::::::::::data');
      if (data) {
        set(() => ({ isPrivateChatOpen: data }));
      } else {
        set(() => ({
          privateChat: [],
          userId: null,
          isPrivateChatOpen: data,
          recipientUser: null,
        }));
      }
    },
    setUserId: (data) => {
      set(() => ({ userId: data }));
    },
    setRecipientUser: (data) => {
      set(() => ({ recipientUser: data }));
    },
    setPrivateChat: (data) => {
      console.log(data, '::::::::::::123 data');
      set(() => ({ privateChat: data?.slice().reverse() }));
    },
    appendPrivateChat: (message) => {
      set((state) => ({ privateChat: [...state.privateChat, message] }));
    },
  }),
  //   {
  //     name: 'usePrivateChatStore',
  //   },
  // ),
);

export default usePrivateChatStore;
