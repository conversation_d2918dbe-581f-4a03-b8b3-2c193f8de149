import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const usePublicChatsStore = create(
  persist(
    (set) => ({
      chats: [],
      livePlayersCount: 0,
      setLivePlayersCount: (data) => {
        set(() => ({ livePlayersCount: data }));
      },
      setChats: (data) => {
        set(() => ({ chats: data.slice().reverse() }));
      },
      appendChat: (message) => {
        set((state) => ({
          chats: [...state.chats, { ...message, isSocketMessage: true }],
        }));
        console.log('new message', message);
      },
      updateRainChat: (message) => {
        set((state) => {
          const updatedChats = state.chats.map((chat) => {
            if (
              chat.rainDrop &&
              chat.rainDrop.rainId === message.rainDrop.rainId
            ) {
              return {
                ...chat,
                rainDrop: { ...chat.rainDrop, status: 'complete' },
              };
            }
            return chat;
          });
          return { chats: updatedChats };
        });
      },
      updateGrabbedRainChat: (message, userId) => {
        console.log('updateGrabbedRainChat 2', message);
        set((state) => {
          const updatedChats = state.chats.map((chat) => {
            if (
              chat.rainDrop &&
              chat.rainDrop.rainId == message.rainDrop.rainId
            ) {
              console.log('updateGrabbedRainChat inside 3', message);
              chat.rainDrop.grabbedStatus = true;
              return { ...chat };
            }
            return chat;
          });
          return { chats: updatedChats };
        });
      },
    }),
    {
      name: 'usePublicChatsStore',
    },
  ),
);

export default usePublicChatsStore;
