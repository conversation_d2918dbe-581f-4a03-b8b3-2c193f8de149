import { create } from 'zustand';

const useVoiceCallStore = create((set) => ({
  voiceCall:null,
  setVoiceCall:(data)=>{
    console.log(data, "::::::::data")
    set(()=>({
      voiceCall:{
        channelName: data?.channelName,
        role:data?.role,
        callLogId:data?.callLogId,
        userId:data?.userId,
        username:data?.username,
        profileImage:data?.profileImage,
        groupId:data?.groupId|| null,
        groupCallMembers:data?.groupCallMembers|| []
      }
    }))
  },
    // 👇 New function to update ONLY groupCallMembers
  updateGroupCallMembers: (members) => {
    set((state) => ({
      voiceCall: {
        ...state.voiceCall,
        groupCallMembers: members
      }
    }));
  },
}));

export default useVoiceCallStore;
