'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import usePrivateChatStore from './usePrivateChatStore';
const useAuthStore = create(
  persist(
    (set) => ({
      isAuthenticated: false,
      coin: 'SC',
      userDetails: null,
      userWallet: null,
      hasRehydrated: false,
      setUserDetails: (data) => {
        console.log(data,":::::::setUserWallet data this is also called? profile???")
        set(() => ({
          userDetails: data,
          userWallet: {
            scCoin:
              (data?.userWallet?.wsc || 0) +
              (data?.userWallet?.psc || 0) +
              (data?.userWallet?.bsc || 0),
            gcCoin: (data?.userWallet?.gcCoin || 0)
          },
        }));
      },
      setUserWallet: (data) => {
        console.log(data, ":::::::setUserWallet data")
        set(() => ({
          userWallet: {
            scCoin: data?.scCoin,
            gcCoin:data?.gcCoin
          },
        }));
      },
      setIsAuthenticated: (data) => {
        set(() => ({ isAuthenticated: data }));
      },
      logout: () => {
        set({ isAuthenticated: false, userDetails: null });
        usePrivateChatStore.setState({
          privateChat: [],
          userId: null,
          isPrivateChatOpen: false,
          recipientUser: null,
          isCallActive: false,
        });
      },
      setCoin: (data) => {
        set(() => ({ coin: data }));
      },
      setHasRehydrated: (data) => {
        set(() => ({ hasRehydrated: data }));
      },
    }),
    {
      name: 'useAuthStore',
      onRehydrateStorage: () => (state) => {
        state.setHasRehydrated(true);
      },
    },
  ),
);

export default useAuthStore;
